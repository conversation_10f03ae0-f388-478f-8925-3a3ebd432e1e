<?php

namespace App\Models;

/**
 * Project Document Model
 * 
 * Handles project document uploads with categorization and metadata.
 */
class ProjectDocumentModel extends BaseModel
{
    protected $table      = 'project_documents';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'document_type', 'title', 'file_name', 'file_path', 'file_size',
        'uploaded_by', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'document_type'  => 'required|in_list[proposal,contract,report,evidence,other]',
        'title'          => 'required|max_length[200]',
        'file_name'      => 'required|max_length[255]',
        'file_path'      => 'required|max_length[500]',
        'file_size'      => 'required|integer'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'document_type' => [
            'required' => 'Document type is required'
        ],
        'title' => [
            'required' => 'Document title is required'
        ],
        'file_name' => [
            'required' => 'File name is required'
        ],
        'file_path' => [
            'required' => 'File path is required'
        ]
    ];
    
    /**
     * Get documents by project
     */
    public function getByProject(int $projectId, ?string $documentType = null): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($documentType) {
            $query = $query->where('document_type', $documentType);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get documents by type
     */
    public function getByType(string $documentType): array
    {
        return $this->select('project_documents.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_documents.project_id')
                   ->where('project_documents.document_type', $documentType)
                   ->orderBy('project_documents.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Upload document
     */
    public function uploadDocument(array $documentData): bool
    {
        $documentData['uploaded_by'] = session()->get('admin_user_id');
        $documentData['created_by'] = session()->get('admin_user_id');
        $documentData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->insert($documentData) !== false;
    }
    
    /**
     * Delete document
     */
    public function deleteDocument(int $documentId, ?int $deletedBy = null): bool
    {
        $document = $this->find($documentId);
        
        if (!$document) {
            return false;
        }
        
        // Soft delete the record
        $result = $this->update($documentId, [
            'deleted_by' => $deletedBy,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            // Delete physical file
            if (file_exists($document['file_path'])) {
                unlink($document['file_path']);
            }
        }
        
        return $result;
    }
    
    /**
     * Get document statistics
     */
    public function getDocumentStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total documents
        $stats['total_documents'] = $query->countAllResults();
        
        // Total file size
        $sizeResult = $query->selectSum('file_size')->first();
        $stats['total_size'] = (int) ($sizeResult['file_size'] ?? 0);
        
        // Documents by type
        $typeCounts = $this->select('document_type, COUNT(*) as count')
                          ->groupBy('document_type')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'document_type');
        
        // Recent uploads (last 30 days)
        $recentDate = date('Y-m-d', strtotime('-30 days'));
        $recentQuery = $this->where('created_at >=', $recentDate);
        if ($projectId) {
            $recentQuery = $recentQuery->where('project_id', $projectId);
        }
        $stats['recent_uploads'] = $recentQuery->countAllResults();
        
        // Average file size
        $stats['average_size'] = $stats['total_documents'] > 0 
            ? round($stats['total_size'] / $stats['total_documents'], 2) 
            : 0;
        
        return $stats;
    }
    
    /**
     * Get large documents
     */
    public function getLargeDocuments(int $sizeThreshold = 10485760): array // 10MB default
    {
        return $this->where('file_size >', $sizeThreshold)
                   ->orderBy('file_size', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get recent uploads
     */
    public function getRecentUploads(int $days = 7, ?int $projectId = null): array
    {
        $dateThreshold = date('Y-m-d', strtotime("-{$days} days"));
        
        $query = $this->select('project_documents.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_documents.project_id')
                     ->where('project_documents.created_at >=', $dateThreshold);
        
        if ($projectId) {
            $query = $query->where('project_documents.project_id', $projectId);
        }
        
        return $query->orderBy('project_documents.created_at', 'DESC')->findAll();
    }
    
    /**
     * Search documents
     */
    public function searchDocuments(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->groupStart()
                     ->like('title', $searchTerm)
                     ->orLike('file_name', $searchTerm)
                     ->groupEnd();
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Check if file exists
     */
    public function fileExists(string $filePath): bool
    {
        return $this->where('file_path', $filePath)->countAllResults() > 0;
    }
    
    /**
     * Get document with project info
     */
    public function getDocumentWithProject(int $documentId): ?array
    {
        return $this->select('project_documents.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_documents.project_id')
                   ->where('project_documents.id', $documentId)
                   ->first();
    }
    
    /**
     * Update document title
     */
    public function updateTitle(int $documentId, string $title, ?int $updatedBy = null): bool
    {
        return $this->update($documentId, [
            'title' => $title,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Get documents count by project
     */
    public function getDocumentCountByProject(int $projectId): int
    {
        return $this->where('project_id', $projectId)->countAllResults();
    }
}
