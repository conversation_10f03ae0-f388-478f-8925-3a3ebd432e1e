<?php
/**
 * Simple test script to verify Countries CRUD functionality
 * Run this from the browser: http://localhost/promis_two/test_countries_crud.php
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

// Get database connection
$db = \Config\Database::connect();

echo "<h1>Countries CRUD Test</h1>";

// Test 1: Check if countries table exists and show structure
echo "<h2>1. Database Structure Check</h2>";
try {
    $query = $db->query("DESCRIBE countries");
    $fields = $query->getResultArray();
    
    echo "<h3>Countries table structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($fields as $field) {
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Null']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td>{$field['Default']}</td>";
        echo "<td>{$field['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 2: Check existing countries
echo "<h2>2. Existing Countries</h2>";
try {
    $query = $db->query("SELECT * FROM countries WHERE deleted_at IS NULL");
    $countries = $query->getResultArray();
    
    if (empty($countries)) {
        echo "<p>No countries found in database.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>ISO2</th><th>ISO3</th><th>Name</th><th>Map Center Lat</th><th>Map Center Lng</th><th>Map Zoom</th><th>Created At</th></tr>";
        foreach ($countries as $country) {
            echo "<tr>";
            echo "<td>{$country['id']}</td>";
            echo "<td>{$country['iso2']}</td>";
            echo "<td>{$country['iso3']}</td>";
            echo "<td>{$country['name']}</td>";
            echo "<td>{$country['map_centre_lat']}</td>";
            echo "<td>{$country['map_centre_lng']}</td>";
            echo "<td>{$country['map_zoom']}</td>";
            echo "<td>{$country['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 3: Test CountryModel functionality
echo "<h2>3. CountryModel Test</h2>";
try {
    $countryModel = new \App\Models\CountryModel();
    
    // Test insert
    $testData = [
        'iso2' => 'TS',
        'iso3' => 'TST',
        'name' => 'Test Country',
        'map_centre_lat' => -6.314993,
        'map_centre_lng' => 143.95555,
        'map_zoom' => 6,
        'created_by' => 1
    ];
    
    echo "<h3>Testing insert with data:</h3>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    $result = $countryModel->insert($testData);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Insert successful! New ID: " . $countryModel->getInsertID() . "</p>";
        
        // Test find
        $newCountry = $countryModel->find($countryModel->getInsertID());
        echo "<h3>Retrieved country:</h3>";
        echo "<pre>" . print_r($newCountry, true) . "</pre>";
        
        // Clean up - delete the test record
        $countryModel->delete($countryModel->getInsertID());
        echo "<p style='color: blue;'>Test record cleaned up.</p>";
        
    } else {
        echo "<p style='color: red;'>✗ Insert failed!</p>";
        $errors = $countryModel->errors();
        if (!empty($errors)) {
            echo "<h3>Validation errors:</h3>";
            echo "<pre>" . print_r($errors, true) . "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Test 4: Check session functionality
echo "<h2>4. Session Test</h2>";
session_start();
echo "<h3>Current session data:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<h2>Test Complete</h2>";
echo "<p><a href='http://localhost/promis_two/dakoii/government/countries/create'>Go to Countries Create Form</a></p>";
?>
