<?php
/**
 * Direct test of country creation without going through the full MVC stack
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

echo "<h1>Direct Country Creation Test</h1>";

// Simulate POST data
$_POST = [
    'name' => 'Test Country Direct',
    'iso2' => 'TD',
    'iso3' => 'TDR',
    'map_centre_gps' => '-6.314993,143.95555',
    'map_zoom' => '6',
    'geojson_id' => 'test_id'
];

echo "<h2>Simulated POST Data</h2>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

try {
    // Create controller instance
    $controller = new \App\Controllers\DakoiiGovernmentController();
    
    // Set up request object
    $request = \Config\Services::request();
    $request->setMethod('POST');
    
    // Set up session with fake user
    $session = \Config\Services::session();
    $session->set('dakoii_user_id', 1);
    $session->set('dakoii_logged_in', true);
    
    echo "<h2>Session Setup</h2>";
    echo "<p>Set dakoii_user_id: 1</p>";
    echo "<p>Set dakoii_logged_in: true</p>";
    
    // Test the model directly first
    echo "<h2>Direct Model Test</h2>";
    $model = new \App\Models\CountryModel();
    
    $testData = [
        'iso2' => 'DT',
        'iso3' => 'DTT',
        'name' => 'Direct Test Country',
        'map_centre_lat' => -6.314993,
        'map_centre_lng' => 143.95555,
        'map_zoom' => 6,
        'created_by' => 1
    ];
    
    echo "<h3>Test Data for Model:</h3>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    // Check if this data already exists
    $existing = $model->where('iso2', $testData['iso2'])->where('deleted_at', null)->first();
    if ($existing) {
        echo "<p style='color: orange;'>Record with ISO2 'DT' already exists, deleting it first...</p>";
        $model->delete($existing['id']);
    }
    
    $result = $model->insert($testData);
    
    if ($result) {
        $insertId = $model->getInsertID();
        echo "<p style='color: green;'>✓ Direct model insert successful! ID: $insertId</p>";
        
        // Retrieve and show the record
        $newRecord = $model->find($insertId);
        echo "<h3>Inserted Record:</h3>";
        echo "<pre>" . print_r($newRecord, true) . "</pre>";
        
        // Clean up
        $model->delete($insertId);
        echo "<p style='color: blue;'>Test record cleaned up.</p>";
        
    } else {
        echo "<p style='color: red;'>✗ Direct model insert failed!</p>";
        $errors = $model->errors();
        echo "<h3>Model Errors:</h3>";
        echo "<pre>" . print_r($errors, true) . "</pre>";
    }
    
    // Test the controller method directly
    echo "<h2>Controller Method Test</h2>";
    
    // Check if the method exists
    if (method_exists($controller, 'storeCountry')) {
        echo "<p>✓ storeCountry method exists</p>";
        
        // We can't easily test the controller method without proper request setup
        echo "<p>Note: Controller method testing requires proper request setup which is complex in this context.</p>";
        
    } else {
        echo "<p style='color: red;'>✗ storeCountry method does not exist!</p>";
    }
    
} catch (\Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Recommendations</h2>";
echo "<ol>";
echo "<li>If the direct model test works, the issue is likely in the controller or routing</li>";
echo "<li>If the direct model test fails, the issue is in the model or database</li>";
echo "<li>Check the browser developer tools Network tab when submitting the actual form</li>";
echo "<li>Check if the form is actually reaching the controller by looking at logs</li>";
echo "</ol>";

echo "<p><a href='debug_auth_filter.php'>Back to Auth Debug</a></p>";
?>
