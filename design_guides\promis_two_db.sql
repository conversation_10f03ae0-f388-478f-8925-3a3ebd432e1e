-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 21, 2025 at 02:39 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `promis_two_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `audit_logs`
--

CREATE TABLE `audit_logs` (
  `id` bigint(20) NOT NULL,
  `table_name` varchar(100) NOT NULL COMMENT 'Name of the table that was modified',
  `primary_key` varchar(64) NOT NULL COMMENT 'Primary key value of the affected record',
  `action` enum('create','update','delete','login','logout','access') NOT NULL COMMENT 'Type of operation performed',
  `old_data` mediumtext DEFAULT NULL COMMENT 'JSON of data before change (for updates/deletes)',
  `new_data` mediumtext DEFAULT NULL COMMENT 'JSON of data after change (for creates/updates)',
  `user_id` bigint(20) DEFAULT NULL COMMENT 'ID of the user who performed the action',
  `username` varchar(50) DEFAULT NULL COMMENT 'Username of the user',
  `user_type` varchar(20) DEFAULT NULL COMMENT 'Type of user (dakoii_user, admin_user, project_officer, etc.)',
  `user_full_name` varchar(100) DEFAULT NULL COMMENT 'Full name of the user',
  `organization_id` bigint(20) DEFAULT NULL COMMENT 'ID of the organization the user belongs to',
  `organization_name` varchar(100) DEFAULT NULL COMMENT 'Name of the organization',
  `organization_type` varchar(50) DEFAULT NULL COMMENT 'Type of organization (NGO, Government, etc.)',
  `project_id` bigint(20) DEFAULT NULL COMMENT 'ID of the project if action is project-related',
  `project_title` varchar(200) DEFAULT NULL COMMENT 'Title of the project',
  `portal` enum('dakoii','admin','monitor') NOT NULL COMMENT 'Portal where the action was performed',
  `module` varchar(50) DEFAULT NULL COMMENT 'Module/section within the portal',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the user',
  `user_agent` text DEFAULT NULL COMMENT 'Browser/client user agent string',
  `session_id` varchar(128) DEFAULT NULL COMMENT 'Session ID',
  `request_url` varchar(255) DEFAULT NULL COMMENT 'URL where the action was performed',
  `description` text DEFAULT NULL COMMENT 'Human-readable description of the action',
  `created_at` datetime DEFAULT NULL COMMENT 'When the audit log was created'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `audit_logs`
--

INSERT INTO `audit_logs` (`id`, `table_name`, `primary_key`, `action`, `old_data`, `new_data`, `user_id`, `username`, `user_type`, `user_full_name`, `organization_id`, `organization_name`, `organization_type`, `project_id`, `project_title`, `portal`, `module`, `ip_address`, `user_agent`, `session_id`, `request_url`, `description`, `created_at`) VALUES
(5, 'dakoii_users', '2', 'logout', NULL, '{\"description\":\"Dakoii user fkenny logged out\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', NULL, NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '6b1d18f61efb48e2a6053582f9f3b37d', 'http://localhost/promis_two/dakoii/logout', 'Dakoii user fkenny logged out', '2025-06-20 01:34:58'),
(6, 'dakoii_users', '2', 'login', NULL, '{\"description\":\"Dakoii user fkenny logged in successfully\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'aaeaf0c825ea02c95906ca37493976d7', 'http://localhost/promis_two/dakoii/authenticate', 'Dakoii user fkenny logged in successfully', '2025-06-20 01:35:04'),
(7, 'dakoii_users', '2', 'logout', NULL, '{\"description\":\"Dakoii user fkenny logged out\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '505ec046d1272ad1ca8924b0d8badefd', 'http://localhost/promis_two/dakoii/logout', 'Dakoii user fkenny logged out', '2025-06-20 03:59:08'),
(8, 'dakoii_users', '2', 'login', NULL, '{\"description\":\"Dakoii user fkenny logged in successfully\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '9cb8cabbdd9b4a0847bd02da07b96c11', 'http://localhost/promis_two/dakoii/authenticate', 'Dakoii user fkenny logged in successfully', '2025-06-20 03:59:19'),
(9, 'dakoii_users', '2', 'logout', NULL, '{\"description\":\"Dakoii user fkenny logged out\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'ec0d5f95aabacec36c92916dbd095b5a', 'http://localhost/promis_two/dakoii/logout', 'Dakoii user fkenny logged out', '2025-06-20 04:56:54'),
(10, 'dakoii_users', '2', 'login', NULL, '{\"description\":\"Dakoii user fkenny logged in successfully\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'ca5f179dfea67d8d6d37394bf976d52d', 'http://localhost/promis_two/dakoii/authenticate', 'Dakoii user fkenny logged in successfully', '2025-06-20 04:57:05'),
(11, 'dakoii_users', '2', 'login', NULL, '{\"description\":\"Dakoii user fkenny logged in successfully\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '97d1bcc4b00cac88c68ee556f432cb2f', 'http://localhost/promis_two/dakoii/authenticate', 'Dakoii user fkenny logged in successfully', '2025-06-20 08:02:01'),
(12, 'dakoii_users', '2', 'login', NULL, '{\"description\":\"Dakoii user fkenny logged in successfully\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2d173890253969b9bba2a76295f93384', 'http://localhost/promis_two/dakoii/authenticate', 'Dakoii user fkenny logged in successfully', '2025-06-21 01:16:16'),
(13, 'dakoii_users', '2', 'logout', NULL, '{\"description\":\"Dakoii user fkenny logged out\",\"event_type\":\"authentication\"}', 2, 'fkenny', 'dakoii_user', 'F Kenny', NULL, 'Dakoii System Administration', 'System', NULL, NULL, 'dakoii', 'authentication', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'aae2d807556fba39c45a800c099257b9', 'http://localhost/promis_two/dakoii/logout', 'Dakoii user fkenny logged out', '2025-06-21 04:33:52');

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `iso2` char(2) NOT NULL,
  `iso3` char(3) NOT NULL,
  `name` varchar(100) NOT NULL,
  `map_zoom` tinyint(3) UNSIGNED DEFAULT NULL,
  `map_centre_gps` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`id`, `iso2`, `iso3`, `name`, `map_zoom`, `map_centre_gps`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(2, 'TF', 'TFM', 'Test Country Form', 6, NULL, '2025-06-20 01:44:44', '2025-06-20 01:46:42', '2025-06-20 01:46:42', 2, NULL, 2),
(3, 'PG', 'PNG', 'Papua New Guinea', 6, NULL, '2025-06-20 01:59:00', '2025-06-20 01:59:00', NULL, 2, NULL, NULL),
(4, 'FJ', 'FJI', 'Fiji', 6, '', '2025-06-20 05:47:25', '2025-06-20 05:47:25', NULL, 2, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_users`
--

CREATE TABLE `dakoii_users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_code` varchar(12) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `name` varchar(100) NOT NULL,
  `role` enum('admin','moderator','user') NOT NULL DEFAULT 'user',
  `id_photo_path` varchar(255) DEFAULT NULL,
  `password_hash` varchar(255) NOT NULL,
  `activation_token` char(64) DEFAULT NULL,
  `is_activated` tinyint(1) NOT NULL DEFAULT 0,
  `password_reset_token` char(64) DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `dakoii_users`
--

INSERT INTO `dakoii_users` (`id`, `user_code`, `username`, `email`, `name`, `role`, `id_photo_path`, `password_hash`, `activation_token`, `is_activated`, `password_reset_token`, `last_login_at`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 'ADMIN001', 'admin', '<EMAIL>', 'Dakoii Administrator', 'admin', NULL, '$argon2id$v=19$m=65536,t=4,p=1$U3ZDcnd2VU9kVzhEWDF1bQ$zOwlXDpucTP/k8L0IGxqpO7A72Qlbkl2DoZvkEkwWtE', NULL, 1, NULL, '2025-06-19 03:25:57', '2025-06-19 03:19:59', '2025-06-19 03:25:57', NULL, 1, 1, NULL),
(2, 'ADMIN002', 'fkenny', '<EMAIL>', 'F Kenny', 'admin', NULL, '$argon2id$v=19$m=65536,t=4,p=1$dk4vcmVNTTdaSU5qRUk3cQ$oSFTa0eqvH8+HXbfw31ggfM2yX68F2Ckwsb6qwqdKt8', NULL, 1, NULL, '2025-06-21 01:16:16', '2025-06-19 03:20:00', '2025-06-21 01:16:16', NULL, 1, 2, NULL),
(3, 'QWF6QHHIKR5', 'aitape', '<EMAIL>', 'Aitape ITU Boy', 'user', 'public/uploads/users/1750311983_ac90c8480870afedb387.png', '$argon2id$v=19$m=65536,t=4,p=1$bk93R1U2cnEvRVk2NHN6Zg$O7DFWPCu0kFYyG3O1VG3jN2hVelkMZ1MuCgDyLMLmv8', NULL, 1, NULL, '2025-06-19 06:10:54', '2025-06-19 04:50:19', '2025-06-20 01:11:09', NULL, 1, 2, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `districts`
--

CREATE TABLE `districts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `province_id` bigint(20) UNSIGNED NOT NULL,
  `dist_code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `geojson_id` varchar(50) DEFAULT NULL COMMENT 'ID of the feature in the GeoJSON boundary layer',
  `map_zoom` tinyint(3) UNSIGNED DEFAULT NULL,
  `map_centre_gps` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `districts`
--

INSERT INTO `districts` (`id`, `province_id`, `dist_code`, `name`, `geojson_id`, `map_zoom`, `map_centre_gps`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 'OBW', 'Obura - Wonenara District', 'OCNPNG00201106', 10, '-7.018713, 145.825926', '2025-06-20 04:01:30', '2025-06-21 02:47:23', NULL, 2, 2, NULL),
(2, 1, 'KTU', 'Kainantu District', NULL, 10, '', '2025-06-20 05:37:57', '2025-06-20 05:37:57', NULL, 2, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `llgs`
--

CREATE TABLE `llgs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `district_id` bigint(20) UNSIGNED NOT NULL,
  `llg_code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `llg_type` enum('urban','rural') NOT NULL DEFAULT 'rural',
  `geojson_id` varchar(50) DEFAULT NULL,
  `map_zoom` tinyint(3) UNSIGNED DEFAULT NULL,
  `map_centre_gps` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `llgs`
--

INSERT INTO `llgs` (`id`, `district_id`, `llg_code`, `name`, `llg_type`, `geojson_id`, `map_zoom`, `map_centre_gps`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 'YELIA', 'Yelia LLG', 'rural', NULL, 12, NULL, '2025-06-20 04:02:41', '2025-06-20 04:02:41', NULL, 2, NULL, NULL),
(2, 1, 'LAMARI', 'Lamari LLG', 'rural', 'OCNPNG0020110608', 12, NULL, '2025-06-20 05:39:57', '2025-06-20 08:31:33', NULL, 2, 2, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `version` varchar(255) NOT NULL,
  `class` varchar(255) NOT NULL,
  `group` varchar(255) NOT NULL,
  `namespace` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  `batch` int(11) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `version`, `class`, `group`, `namespace`, `time`, `batch`) VALUES
(1, '2025-06-20-004642', 'App\\Database\\Migrations\\CreateUnifiedAuditLogsTable', 'default', 'App', **********, 1);

-- --------------------------------------------------------

--
-- Table structure for table `organizations`
--

CREATE TABLE `organizations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `org_code` char(5) NOT NULL,
  `name` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `license_status` enum('paid','unpaid') NOT NULL DEFAULT 'paid',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `logo_path` varchar(255) DEFAULT NULL,
  `wallpaper_path` varchar(255) DEFAULT NULL,
  `contact_email` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(30) DEFAULT NULL,
  `address_line1` varchar(150) DEFAULT NULL,
  `address_line2` varchar(150) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `hq_lat` decimal(10,8) DEFAULT NULL,
  `hq_lng` decimal(11,8) DEFAULT NULL,
  `website_url` varchar(150) DEFAULT NULL,
  `facebook_url` varchar(150) DEFAULT NULL,
  `twitter_url` varchar(150) DEFAULT NULL,
  `linkedin_url` varchar(150) DEFAULT NULL,
  `instagram_url` varchar(150) DEFAULT NULL,
  `country_lock` varchar(20) DEFAULT NULL,
  `province_lock` varchar(20) DEFAULT NULL,
  `district_lock` varchar(20) DEFAULT NULL,
  `llg_lock` varchar(20) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `organizations`
--

INSERT INTO `organizations` (`id`, `org_code`, `name`, `description`, `license_status`, `is_active`, `logo_path`, `wallpaper_path`, `contact_email`, `contact_phone`, `address_line1`, `address_line2`, `city`, `state`, `postal_code`, `country`, `hq_lat`, `hq_lng`, `website_url`, `facebook_url`, `twitter_url`, `linkedin_url`, `instagram_url`, `country_lock`, `province_lock`, `district_lock`, `llg_lock`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(2, '68997', 'East Sepik Provincial Administration', 'This is ESP', 'paid', 1, 'public/uploads/organizations/2/logos/6853b5274cf74.png', 'public/uploads/organizations/2/wallpapers/6853b543ab6a2.png', '<EMAIL>', '4254354', 'Address', 'Adrere2', 'Wewad', 'ESP', '3345', 'Papua New Guinea', NULL, NULL, 'https://www.esp.gov.pg', 'https://www.esp.gov.pg/fb', 'https://www.esp.gov.pg/tweeter', 'https://www.esp.gov.pg/linkedin', 'https://www.esp.gov.pg/insta', NULL, NULL, NULL, NULL, '2025-06-19 06:00:04', '2025-06-19 06:59:15', NULL, 1, 1, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `organization_images`
--

CREATE TABLE `organization_images` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `organization_id` bigint(20) UNSIGNED NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `caption` varchar(150) DEFAULT NULL,
  `sort_order` tinyint(3) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `projects`
--

CREATE TABLE `projects` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `org_id` bigint(20) UNSIGNED NOT NULL,
  `pro_code` varchar(20) NOT NULL,
  `other_project_ids` text DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `goal` text DEFAULT NULL,
  `description` mediumtext DEFAULT NULL,
  `initiation_date` date DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `address_line` varchar(200) DEFAULT NULL,
  `country_id` bigint(20) UNSIGNED DEFAULT NULL,
  `province_id` bigint(20) UNSIGNED DEFAULT NULL,
  `district_id` bigint(20) UNSIGNED DEFAULT NULL,
  `llg_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ward_name` varchar(100) DEFAULT NULL,
  `village_name` varchar(100) DEFAULT NULL,
  `gps_point` varchar(20) DEFAULT NULL,
  `gps_kml_path` varchar(255) DEFAULT NULL,
  `status` enum('planning','active','on-hold','completed','cancelled') DEFAULT 'planning',
  `status_notes` varchar(255) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_by_id` bigint(20) UNSIGNED DEFAULT NULL,
  `officer_certified` tinyint(1) DEFAULT 0,
  `officer_cert_at` datetime DEFAULT NULL,
  `officer_cert_by` bigint(20) UNSIGNED DEFAULT NULL,
  `contractor_certified` tinyint(1) DEFAULT 0,
  `contractor_cert_at` datetime DEFAULT NULL,
  `contractor_cert_by` bigint(20) UNSIGNED DEFAULT NULL,
  `evaluation_file` varchar(255) DEFAULT NULL,
  `evaluation_notes` text DEFAULT NULL,
  `evaluation_date` date DEFAULT NULL,
  `evaluation_by` bigint(20) UNSIGNED DEFAULT NULL,
  `baseline_year` smallint(5) UNSIGNED DEFAULT NULL,
  `target_year` smallint(5) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_budget_items`
--

CREATE TABLE `project_budget_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `item_code` varchar(30) NOT NULL,
  `description` varchar(255) NOT NULL,
  `amount_planned` decimal(15,2) NOT NULL,
  `status` enum('active','removed') DEFAULT 'active',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_contractors`
--

CREATE TABLE `project_contractors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `contractor_id` bigint(20) UNSIGNED NOT NULL,
  `joined_at` date NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `removal_reason` text DEFAULT NULL,
  `client_flag` enum('positive','neutral','negative') DEFAULT 'neutral',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_documents`
--

CREATE TABLE `project_documents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `milestone_id` bigint(20) UNSIGNED DEFAULT NULL,
  `doc_path` varchar(255) NOT NULL,
  `doc_type` varchar(50) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `version_no` smallint(5) UNSIGNED DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_expenses`
--

CREATE TABLE `project_expenses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `milestone_id` bigint(20) UNSIGNED DEFAULT NULL,
  `description` varchar(255) NOT NULL,
  `amount_paid` decimal(15,2) NOT NULL,
  `paid_on` date NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_impact_indicators`
--

CREATE TABLE `project_impact_indicators` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `indicator_text` varchar(255) NOT NULL,
  `baseline_value` decimal(15,4) DEFAULT NULL,
  `baseline_date` date DEFAULT NULL,
  `target_value` decimal(15,4) DEFAULT NULL,
  `target_date` date DEFAULT NULL,
  `actual_value` decimal(15,4) DEFAULT NULL,
  `actual_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_issues_addressed`
--

CREATE TABLE `project_issues_addressed` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `issue_type` enum('direct','indirect') NOT NULL,
  `description` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_milestones`
--

CREATE TABLE `project_milestones` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `phase_id` bigint(20) UNSIGNED NOT NULL,
  `milestone_code` varchar(20) NOT NULL,
  `title` varchar(200) NOT NULL,
  `description` text DEFAULT NULL,
  `target_date` date DEFAULT NULL,
  `status` enum('pending','in-progress','completed','approved') DEFAULT 'pending',
  `completion_date` date DEFAULT NULL,
  `evidence_count` smallint(5) UNSIGNED DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_officers`
--

CREATE TABLE `project_officers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `role` enum('lead','certifier','support') NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `removal_reason` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_outcomes`
--

CREATE TABLE `project_outcomes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `outcome_text` varchar(255) NOT NULL,
  `quantity` decimal(12,2) NOT NULL DEFAULT 1.00,
  `unit` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_phases`
--

CREATE TABLE `project_phases` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `phase_code` varchar(20) NOT NULL,
  `title` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `sort_order` smallint(5) UNSIGNED NOT NULL DEFAULT 1,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','deactivated') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_risks`
--

CREATE TABLE `project_risks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `milestone_id` bigint(20) UNSIGNED DEFAULT NULL,
  `risk_type` enum('proposed','foreseen','witnessed') NOT NULL,
  `risk_level` enum('low','medium','high','critical') NOT NULL,
  `description` text NOT NULL,
  `mitigation` text DEFAULT NULL,
  `approval_status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `provinces`
--

CREATE TABLE `provinces` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `country_id` bigint(20) UNSIGNED NOT NULL,
  `prov_code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `geojson_id` varchar(50) DEFAULT NULL COMMENT 'ID of the feature in the GeoJSON boundary layer',
  `map_zoom` tinyint(3) UNSIGNED DEFAULT NULL,
  `map_centre_gps` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `provinces`
--

INSERT INTO `provinces` (`id`, `country_id`, `prov_code`, `name`, `geojson_id`, `map_zoom`, `map_centre_gps`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 3, 'EHP', 'Eastern Highlands Province', 'OCNPNG002011', 8, '-7.018713, 145.825926', '2025-06-20 04:00:10', '2025-06-21 04:26:25', NULL, 2, 2, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `organization_id` bigint(20) UNSIGNED NOT NULL,
  `user_code` varchar(12) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `name` varchar(100) NOT NULL,
  `role` enum('admin','moderator','editor','user') NOT NULL DEFAULT 'user',
  `profile_photo_path` varchar(255) DEFAULT NULL,
  `password_hash` varchar(255) NOT NULL,
  `activation_token` char(64) DEFAULT NULL,
  `is_activated` tinyint(1) NOT NULL DEFAULT 0,
  `password_reset_token` char(64) DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `organization_id`, `user_code`, `username`, `email`, `name`, `role`, `profile_photo_path`, `password_hash`, `activation_token`, `is_activated`, `password_reset_token`, `last_login_at`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(2, 2, 'ABLYZ03A', 'aitape', '<EMAIL>', 'Aitape ITU', 'admin', NULL, '$argon2id$v=19$m=65536,t=4,p=1$ZnVlTTEvWUdSSGdqa1dRNw$f0vhmWFZNoUdO2fJnfOwwP2aNTOhmeifWchqrAp6f2g', NULL, 1, NULL, NULL, '2025-06-19 07:36:14', '2025-06-19 07:38:18', NULL, 1, 2, NULL),
(3, 1, 'SXK7DZFY60YB', 'audit_test_user_1750381154', '<EMAIL>', 'Updated Audit Test User', 'user', NULL, '$argon2id$v=19$m=65536,t=4,p=1$R0h2R25OYmFPLzYyejFvLg$iEbZwphd9G2Wm6amVfzuQ0ehIW3rbHSTDwDp9+oykVA', NULL, 1, NULL, NULL, '2025-06-20 00:59:15', '2025-06-20 00:59:15', '2025-06-20 00:59:15', 1, NULL, NULL),
(4, 1, '2AK8JRP4G59X', 'audit_test_user_1750381463', '<EMAIL>', 'Updated Audit Test User', 'user', NULL, '$argon2id$v=19$m=65536,t=4,p=1$cnlkNWt2RmpFVG9HZkJEZg$luLK3xl0dn/x014iwm72LxOBphJym8NGkLFd3h2mVdQ', NULL, 1, NULL, NULL, '2025-06-20 01:04:23', '2025-06-20 01:04:23', '2025-06-20 01:04:23', 1, NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `table_name` (`table_name`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `portal` (`portal`),
  ADD KEY `action` (`action`),
  ADD KEY `created_at` (`created_at`),
  ADD KEY `user_type` (`user_type`),
  ADD KEY `module` (`module`),
  ADD KEY `organization_id` (`organization_id`),
  ADD KEY `project_id` (`project_id`),
  ADD KEY `portal_organization_id` (`portal`,`organization_id`),
  ADD KEY `portal_user_id` (`portal`,`user_id`),
  ADD KEY `organization_id_user_id` (`organization_id`,`user_id`),
  ADD KEY `project_id_action` (`project_id`,`action`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `iso2` (`iso2`),
  ADD UNIQUE KEY `iso3` (`iso3`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_code` (`user_code`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_activation` (`activation_token`),
  ADD KEY `idx_password_reset` (`password_reset_token`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `districts`
--
ALTER TABLE `districts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_province_code` (`province_id`,`dist_code`),
  ADD KEY `idx_province` (`province_id`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `llgs`
--
ALTER TABLE `llgs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_district_code` (`district_id`,`llg_code`),
  ADD KEY `idx_district` (`district_id`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `organizations`
--
ALTER TABLE `organizations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `org_code` (`org_code`),
  ADD KEY `idx_license` (`license_status`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `organization_images`
--
ALTER TABLE `organization_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_org` (`organization_id`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pro_code` (`pro_code`),
  ADD KEY `idx_org` (`org_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_budget_items`
--
ALTER TABLE `project_budget_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_contractors`
--
ALTER TABLE `project_contractors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_contractor` (`project_id`,`contractor_id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_documents`
--
ALTER TABLE `project_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_milestone` (`project_id`,`milestone_id`),
  ADD KEY `idx_version` (`project_id`,`version_no`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_expenses`
--
ALTER TABLE `project_expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_milestone` (`project_id`,`milestone_id`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_impact_indicators`
--
ALTER TABLE `project_impact_indicators`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_issues_addressed`
--
ALTER TABLE `project_issues_addressed`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_milestones`
--
ALTER TABLE `project_milestones`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_phase` (`phase_id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_officers`
--
ALTER TABLE `project_officers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project_user` (`project_id`,`user_id`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_outcomes`
--
ALTER TABLE `project_outcomes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_phases`
--
ALTER TABLE `project_phases`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_sort` (`project_id`,`sort_order`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `project_risks`
--
ALTER TABLE `project_risks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project` (`project_id`),
  ADD KEY `idx_status` (`approval_status`),
  ADD KEY `idx_soft` (`deleted_at`);

--
-- Indexes for table `provinces`
--
ALTER TABLE `provinces`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_country_code` (`country_id`,`prov_code`),
  ADD KEY `idx_country` (`country_id`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_code` (`user_code`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_organization` (`organization_id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_activation` (`activation_token`),
  ADD KEY `idx_password_reset` (`password_reset_token`),
  ADD KEY `idx_soft_delete` (`deleted_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `audit_logs`
--
ALTER TABLE `audit_logs`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `districts`
--
ALTER TABLE `districts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `llgs`
--
ALTER TABLE `llgs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `organizations`
--
ALTER TABLE `organizations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `organization_images`
--
ALTER TABLE `organization_images`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `projects`
--
ALTER TABLE `projects`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_budget_items`
--
ALTER TABLE `project_budget_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_contractors`
--
ALTER TABLE `project_contractors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_documents`
--
ALTER TABLE `project_documents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_expenses`
--
ALTER TABLE `project_expenses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_impact_indicators`
--
ALTER TABLE `project_impact_indicators`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_issues_addressed`
--
ALTER TABLE `project_issues_addressed`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_milestones`
--
ALTER TABLE `project_milestones`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_officers`
--
ALTER TABLE `project_officers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_outcomes`
--
ALTER TABLE `project_outcomes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_phases`
--
ALTER TABLE `project_phases`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_risks`
--
ALTER TABLE `project_risks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `provinces`
--
ALTER TABLE `provinces`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
