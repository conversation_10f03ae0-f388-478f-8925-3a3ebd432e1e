<?php

namespace App\Models;

/**
 * Project Issues Addressed Model
 * 
 * Handles issues that projects address with quantity and unit tracking.
 */
class ProjectIssuesAddressedModel extends BaseModel
{
    protected $table      = 'project_issues_addressed';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'issue_text', 'quantity', 'unit',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'  => 'required|integer',
        'issue_text'  => 'required|max_length[255]',
        'quantity'    => 'required|decimal'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'issue_text' => [
            'required' => 'Issue text is required'
        ],
        'quantity' => [
            'required' => 'Quantity is required'
        ]
    ];
    
    /**
     * Get issues by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get issues by unit
     */
    public function getByUnit(string $unit): array
    {
        return $this->where('unit', $unit)
                   ->orderBy('quantity', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get project issues summary
     */
    public function getProjectIssuesSummary(int $projectId): array
    {
        $issues = $this->getByProject($projectId);
        
        $summary = [
            'total_issues' => count($issues),
            'total_quantity' => 0,
            'by_unit' => []
        ];
        
        foreach ($issues as $issue) {
            $summary['total_quantity'] += $issue['quantity'];
            
            $unit = $issue['unit'] ?: 'No Unit';
            if (!isset($summary['by_unit'][$unit])) {
                $summary['by_unit'][$unit] = [
                    'count' => 0,
                    'total_quantity' => 0
                ];
            }
            
            $summary['by_unit'][$unit]['count']++;
            $summary['by_unit'][$unit]['total_quantity'] += $issue['quantity'];
        }
        
        return $summary;
    }
    
    /**
     * Get issues statistics
     */
    public function getIssuesStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total issues
        $stats['total_issues'] = $query->countAllResults();
        
        // Total quantity
        $totalResult = $query->selectSum('quantity')->first();
        $stats['total_quantity'] = (float) ($totalResult['quantity'] ?? 0);
        
        // Average quantity
        $stats['average_quantity'] = $stats['total_issues'] > 0 
            ? $stats['total_quantity'] / $stats['total_issues'] 
            : 0;
        
        // Issues by unit
        $unitStats = $this->select('unit, COUNT(*) as count, SUM(quantity) as total_quantity')
                         ->groupBy('unit')
                         ->orderBy('count', 'DESC')
                         ->findAll();
        
        $stats['by_unit'] = $unitStats;
        
        return $stats;
    }
    
    /**
     * Get top issues by quantity
     */
    public function getTopIssues(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this;
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('quantity', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
    
    /**
     * Search issues by text
     */
    public function searchIssues(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->like('issue_text', $searchTerm);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get unique units
     */
    public function getUniqueUnits(): array
    {
        return $this->select('unit')
                   ->where('unit IS NOT NULL')
                   ->where('unit !=', '')
                   ->distinct()
                   ->orderBy('unit', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get common issues across projects
     */
    public function getCommonIssues(int $limit = 10): array
    {
        return $this->select('issue_text, COUNT(*) as project_count, SUM(quantity) as total_quantity')
                   ->groupBy('issue_text')
                   ->having('project_count >', 1)
                   ->orderBy('project_count', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
}
