<!DOCTYPE html>
<html>
<head>
    <title>Test Country Creation Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .debug { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Test Country Creation Form</h1>
    
    <div class="debug">
        <h3>Debug Information</h3>
        <p><strong>Form Action:</strong> <span id="form-action"></span></p>
        <p><strong>Current URL:</strong> <span id="current-url"></span></p>
        <p>This form will test the country creation functionality directly.</p>
    </div>

    <form id="test-form" method="POST" action="http://localhost/promis_two/debug/countries/create">
        <div class="form-group">
            <label for="name">Country Name *</label>
            <input type="text" id="name" name="name" value="Test Country Form" required>
        </div>

        <div class="form-group">
            <label for="iso2">ISO2 Code *</label>
            <input type="text" id="iso2" name="iso2" value="TF" maxlength="2" required style="text-transform: uppercase;">
        </div>

        <div class="form-group">
            <label for="iso3">ISO3 Code *</label>
            <input type="text" id="iso3" name="iso3" value="TFM" maxlength="3" required style="text-transform: uppercase;">
        </div>

        <div class="form-group">
            <label for="map_centre_gps">Map Center GPS</label>
            <input type="text" id="map_centre_gps" name="map_centre_gps" value="-6.314993,143.95555" placeholder="lat,lng">
        </div>

        <div class="form-group">
            <label for="map_zoom">Map Zoom Level</label>
            <input type="number" id="map_zoom" name="map_zoom" value="6" min="1" max="20">
        </div>

        <div class="form-group">
            <button type="submit">Create Country</button>
            <button type="button" onclick="testWithAuth()">Test with Auth Route</button>
        </div>
    </form>

    <div class="debug">
        <h3>Test Options</h3>
        <p><button onclick="changeAction('debug')">Use Debug Route (No Auth)</button></p>
        <p><button onclick="changeAction('auth')">Use Auth Route (Requires Login)</button></p>
        <p><button onclick="showFormData()">Show Form Data</button></p>
    </div>

    <div id="form-data" class="debug" style="display: none;">
        <h3>Form Data Preview</h3>
        <pre id="form-data-content"></pre>
    </div>

    <script>
        // Update debug info
        document.getElementById('form-action').textContent = document.getElementById('test-form').action;
        document.getElementById('current-url').textContent = window.location.href;

        // Auto-uppercase ISO codes
        document.getElementById('iso2').addEventListener('input', function(e) {
            e.target.value = e.target.value.toUpperCase();
        });

        document.getElementById('iso3').addEventListener('input', function(e) {
            e.target.value = e.target.value.toUpperCase();
        });

        function changeAction(type) {
            const form = document.getElementById('test-form');
            if (type === 'debug') {
                form.action = 'http://localhost/promis_two/debug/countries/create';
            } else {
                form.action = 'http://localhost/promis_two/dakoii/government/countries/create';
            }
            document.getElementById('form-action').textContent = form.action;
        }

        function testWithAuth() {
            changeAction('auth');
            document.getElementById('test-form').submit();
        }

        function showFormData() {
            const form = document.getElementById('test-form');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            document.getElementById('form-data-content').textContent = JSON.stringify(data, null, 2);
            document.getElementById('form-data').style.display = 'block';
        }

        // Form submission logging
        document.getElementById('test-form').addEventListener('submit', function(e) {
            console.log('Form submitting to:', this.action);
            console.log('Form method:', this.method);
            
            const formData = new FormData(this);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            console.log('Form data:', data);
        });
    </script>
</body>
</html>
