<?php

namespace App\Models;

/**
 * Project Impact Indicator Model
 * 
 * Handles project impact indicators with baseline, target, and actual values.
 */
class ProjectImpactIndicatorModel extends BaseModel
{
    protected $table      = 'project_impact_indicators';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'indicator_text', 'baseline_value', 'target_value', 'actual_value',
        'unit', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'      => 'required|integer',
        'indicator_text'  => 'required|max_length[255]',
        'baseline_value'  => 'required|decimal',
        'target_value'    => 'required|decimal'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'indicator_text' => [
            'required' => 'Indicator text is required'
        ],
        'baseline_value' => [
            'required' => 'Baseline value is required'
        ],
        'target_value' => [
            'required' => 'Target value is required'
        ]
    ];
    
    /**
     * Get indicators by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get indicators by unit
     */
    public function getByUnit(string $unit): array
    {
        return $this->where('unit', $unit)
                   ->orderBy('target_value', 'DESC')
                   ->findAll();
    }
    
    /**
     * Update actual value
     */
    public function updateActualValue(int $indicatorId, float $actualValue, ?int $updatedBy = null): bool
    {
        return $this->update($indicatorId, [
            'actual_value' => $actualValue,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Get project impact summary
     */
    public function getProjectImpactSummary(int $projectId): array
    {
        $indicators = $this->getByProject($projectId);
        
        $summary = [
            'total_indicators' => count($indicators),
            'indicators_with_actual' => 0,
            'targets_achieved' => 0,
            'targets_exceeded' => 0,
            'targets_not_met' => 0,
            'average_achievement_rate' => 0
        ];
        
        $totalAchievementRate = 0;
        $indicatorsWithActual = 0;
        
        foreach ($indicators as $indicator) {
            if ($indicator['actual_value'] !== null) {
                $summary['indicators_with_actual']++;
                $indicatorsWithActual++;
                
                $targetValue = $indicator['target_value'];
                $actualValue = $indicator['actual_value'];
                
                if ($targetValue > 0) {
                    $achievementRate = ($actualValue / $targetValue) * 100;
                    $totalAchievementRate += $achievementRate;
                    
                    if ($actualValue >= $targetValue) {
                        if ($actualValue > $targetValue) {
                            $summary['targets_exceeded']++;
                        } else {
                            $summary['targets_achieved']++;
                        }
                    } else {
                        $summary['targets_not_met']++;
                    }
                }
            }
        }
        
        if ($indicatorsWithActual > 0) {
            $summary['average_achievement_rate'] = round($totalAchievementRate / $indicatorsWithActual, 2);
        }
        
        return $summary;
    }
    
    /**
     * Get indicator performance analysis
     */
    public function getIndicatorPerformance(int $projectId): array
    {
        $indicators = $this->getByProject($projectId);
        $performance = [];
        
        foreach ($indicators as $indicator) {
            $analysis = [
                'id' => $indicator['id'],
                'indicator_text' => $indicator['indicator_text'],
                'baseline_value' => $indicator['baseline_value'],
                'target_value' => $indicator['target_value'],
                'actual_value' => $indicator['actual_value'],
                'unit' => $indicator['unit'],
                'improvement_from_baseline' => null,
                'achievement_rate' => null,
                'status' => 'pending'
            ];
            
            if ($indicator['actual_value'] !== null) {
                // Calculate improvement from baseline
                $baselineValue = $indicator['baseline_value'];
                $actualValue = $indicator['actual_value'];
                
                if ($baselineValue != 0) {
                    $analysis['improvement_from_baseline'] = round((($actualValue - $baselineValue) / $baselineValue) * 100, 2);
                }
                
                // Calculate achievement rate
                $targetValue = $indicator['target_value'];
                if ($targetValue != 0) {
                    $analysis['achievement_rate'] = round(($actualValue / $targetValue) * 100, 2);
                    
                    if ($actualValue >= $targetValue) {
                        $analysis['status'] = $actualValue > $targetValue ? 'exceeded' : 'achieved';
                    } else {
                        $analysis['status'] = 'not_met';
                    }
                }
            }
            
            $performance[] = $analysis;
        }
        
        return $performance;
    }
    
    /**
     * Get indicators statistics
     */
    public function getIndicatorStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total indicators
        $stats['total_indicators'] = $query->countAllResults();
        
        // Indicators with actual values
        $withActualQuery = $this->where('actual_value IS NOT NULL');
        if ($projectId) {
            $withActualQuery = $withActualQuery->where('project_id', $projectId);
        }
        $stats['with_actual_values'] = $withActualQuery->countAllResults();
        
        // Average values
        $avgResult = $query->select('AVG(baseline_value) as avg_baseline, AVG(target_value) as avg_target, AVG(actual_value) as avg_actual')
                          ->first();
        
        $stats['averages'] = [
            'baseline' => round((float) ($avgResult['avg_baseline'] ?? 0), 2),
            'target' => round((float) ($avgResult['avg_target'] ?? 0), 2),
            'actual' => round((float) ($avgResult['avg_actual'] ?? 0), 2)
        ];
        
        // Indicators by unit
        $unitStats = $this->select('unit, COUNT(*) as count')
                         ->groupBy('unit')
                         ->orderBy('count', 'DESC')
                         ->findAll();
        
        $stats['by_unit'] = $unitStats;
        
        return $stats;
    }
    
    /**
     * Get top performing indicators
     */
    public function getTopPerformingIndicators(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this->select('*, (actual_value / target_value * 100) as achievement_rate')
                     ->where('actual_value IS NOT NULL')
                     ->where('target_value >', 0);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('achievement_rate', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
    
    /**
     * Get unique units
     */
    public function getUniqueUnits(): array
    {
        return $this->select('unit')
                   ->where('unit IS NOT NULL')
                   ->where('unit !=', '')
                   ->distinct()
                   ->orderBy('unit', 'ASC')
                   ->findAll();
    }
}
