<?php
/**
 * Debug script to test country creation without authentication
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

echo "<h1>Debug Country Creation</h1>";

try {
    // Load the model
    $model = new \App\Models\CountryModel();
    
    echo "<h2>Model Information</h2>";
    echo "<p>Table: " . $model->getTable() . "</p>";
    echo "<p>Allowed Fields: " . implode(', ', $model->getAllowedFields()) . "</p>";
    
    // Test data
    $testData = [
        'iso2' => 'PG',
        'iso3' => 'PNG',
        'name' => 'Papua New Guinea',
        'map_centre_lat' => -6.314993,
        'map_centre_lng' => 143.95555,
        'map_zoom' => 6,
        'created_by' => 1
    ];
    
    echo "<h2>Test Data</h2>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    // Check if data already exists
    $existingIso2 = $model->where('iso2', $testData['iso2'])->where('deleted_at', null)->first();
    $existingIso3 = $model->where('iso3', $testData['iso3'])->where('deleted_at', null)->first();
    
    echo "<h2>Uniqueness Check</h2>";
    echo "<p>Existing ISO2 (" . $testData['iso2'] . "): " . ($existingIso2 ? 'Found' : 'Not found') . "</p>";
    echo "<p>Existing ISO3 (" . $testData['iso3'] . "): " . ($existingIso3 ? 'Found' : 'Not found') . "</p>";
    
    if ($existingIso2 || $existingIso3) {
        echo "<p style='color: orange;'>Data already exists, skipping insert test.</p>";
        
        // Show existing data
        if ($existingIso2) {
            echo "<h3>Existing ISO2 Record:</h3>";
            echo "<pre>" . print_r($existingIso2, true) . "</pre>";
        }
        if ($existingIso3) {
            echo "<h3>Existing ISO3 Record:</h3>";
            echo "<pre>" . print_r($existingIso3, true) . "</pre>";
        }
    } else {
        // Try to insert
        echo "<h2>Insert Test</h2>";
        
        // Clean data - only keep allowed fields
        $allowedData = [];
        foreach ($model->getAllowedFields() as $field) {
            if (isset($testData[$field])) {
                $allowedData[$field] = $testData[$field];
            }
        }
        
        echo "<h3>Cleaned Data for Insert:</h3>";
        echo "<pre>" . print_r($allowedData, true) . "</pre>";
        
        $result = $model->insert($allowedData);
        
        if ($result) {
            $insertId = $model->getInsertID();
            echo "<p style='color: green;'>✓ Insert successful! New ID: $insertId</p>";
            
            // Retrieve the inserted record
            $newRecord = $model->find($insertId);
            echo "<h3>Inserted Record:</h3>";
            echo "<pre>" . print_r($newRecord, true) . "</pre>";
            
            // Clean up
            $model->delete($insertId);
            echo "<p style='color: blue;'>Test record cleaned up.</p>";
            
        } else {
            echo "<p style='color: red;'>✗ Insert failed!</p>";
            
            $errors = $model->errors();
            if (!empty($errors)) {
                echo "<h3>Validation Errors:</h3>";
                echo "<pre>" . print_r($errors, true) . "</pre>";
            }
            
            // Get last query for debugging
            $db = \Config\Database::connect();
            echo "<h3>Last Query:</h3>";
            echo "<pre>" . $db->getLastQuery() . "</pre>";
        }
    }
    
    // Test form data processing
    echo "<h2>Form Data Processing Test</h2>";
    $formData = [
        'name' => 'Test Country',
        'iso2' => 'TC',
        'iso3' => 'TCT',
        'map_centre_gps' => '-6.314993,143.95555',
        'map_zoom' => '6',
        'geojson_id' => 'some_id'
    ];
    
    echo "<h3>Original Form Data:</h3>";
    echo "<pre>" . print_r($formData, true) . "</pre>";
    
    // Process GPS coordinates
    if (!empty($formData['map_centre_gps'])) {
        $gps = explode(',', $formData['map_centre_gps']);
        if (count($gps) == 2) {
            $formData['map_centre_lat'] = trim($gps[0]);
            $formData['map_centre_lng'] = trim($gps[1]);
        }
        unset($formData['map_centre_gps']);
    }
    
    // Remove geojson_id
    unset($formData['geojson_id']);
    
    // Add audit fields
    $formData['created_by'] = 1;
    
    echo "<h3>Processed Form Data:</h3>";
    echo "<pre>" . print_r($formData, true) . "</pre>";
    
    // Clean data
    $cleanedData = [];
    foreach ($model->getAllowedFields() as $field) {
        if (isset($formData[$field])) {
            $cleanedData[$field] = $formData[$field];
        }
    }
    
    echo "<h3>Final Cleaned Data:</h3>";
    echo "<pre>" . print_r($cleanedData, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<p>1. <a href='http://localhost/promis_two/dakoii'>Login to Dakoii Portal</a></p>";
echo "<p>2. <a href='http://localhost/promis_two/dakoii/government/countries/create'>Test Countries Create Form</a></p>";
echo "<p>3. Use credentials: admin / admin123 or fkenny / dakoii</p>";
?>
