<?php
/**
 * Simple test to check database connection and basic functionality
 */

// Basic database connection test
try {
    $host = '127.0.0.1';
    $dbname = 'promis_two_db';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Database Connection Test</h1>";
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Check countries table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Countries table has {$result['count']} records</p>";
    
    // Test insert
    echo "<h2>Testing Direct Insert</h2>";
    $sql = "INSERT INTO countries (iso2, iso3, name, map_centre_lat, map_centre_lng, map_zoom, created_at, created_by) 
            VALUES ('XX', 'XXX', 'Test Direct Insert', -6.314993, 143.95555, 6, NOW(), 1)";
    
    $stmt = $pdo->prepare($sql);
    if ($stmt->execute()) {
        $insertId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Direct insert successful! ID: $insertId</p>";
        
        // Clean up
        $pdo->exec("DELETE FROM countries WHERE id = $insertId");
        echo "<p style='color: blue;'>Test record cleaned up.</p>";
    } else {
        echo "<p style='color: red;'>✗ Direct insert failed!</p>";
    }
    
    // Show table structure
    echo "<h2>Table Structure</h2>";
    $stmt = $pdo->query("DESCRIBE countries");
    $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($fields as $field) {
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Null']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td>{$field['Default']}</td>";
        echo "<td>{$field['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<h1>Database Error</h1>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Form Test</h2>";
echo "<p>Try submitting this simple form to test the controller:</p>";
echo '<form method="POST" action="http://localhost/promis_two/dakoii/government/countries/create">';
echo '<input type="hidden" name="csrf_test_name" value="test">';
echo '<p>Name: <input type="text" name="name" value="Test Country" required></p>';
echo '<p>ISO2: <input type="text" name="iso2" value="TC" maxlength="2" required></p>';
echo '<p>ISO3: <input type="text" name="iso3" value="TCT" maxlength="3" required></p>';
echo '<p>Map Center GPS: <input type="text" name="map_centre_gps" value="-6.314993,143.95555"></p>';
echo '<p>Map Zoom: <input type="number" name="map_zoom" value="6" min="1" max="20"></p>';
echo '<p><input type="submit" value="Test Submit"></p>';
echo '</form>';

echo "<p><strong>Note:</strong> This form will likely fail due to authentication requirements.</p>";
echo "<p><a href='http://localhost/promis_two/dakoii'>Go to Dakoii Login</a></p>";
?>
