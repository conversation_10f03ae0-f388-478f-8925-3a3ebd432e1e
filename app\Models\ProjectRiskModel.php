<?php

namespace App\Models;

/**
 * Project Risk Model
 * 
 * Handles project risk management with impact and likelihood assessment.
 */
class ProjectRiskModel extends BaseModel
{
    protected $table      = 'project_risks';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'risk_description', 'impact_level', 'likelihood', 'mitigation_strategy',
        'status', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'        => 'required|integer',
        'risk_description'  => 'required|max_length[500]',
        'impact_level'      => 'required|in_list[low,medium,high,critical]',
        'likelihood'        => 'required|in_list[low,medium,high]',
        'status'            => 'in_list[identified,active,mitigated,closed]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'risk_description' => [
            'required' => 'Risk description is required'
        ],
        'impact_level' => [
            'required' => 'Impact level is required'
        ],
        'likelihood' => [
            'required' => 'Likelihood is required'
        ]
    ];
    
    /**
     * Get risks by project
     */
    public function getByProject(int $projectId, ?string $status = null): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get risks by status
     */
    public function getByStatus(string $status): array
    {
        return $this->select('project_risks.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_risks.project_id')
                   ->where('project_risks.status', $status)
                   ->orderBy('project_risks.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get active risks
     */
    public function getActiveRisks(?int $projectId = null): array
    {
        $query = $this->whereIn('status', ['identified', 'active']);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('impact_level', 'DESC')
                    ->orderBy('likelihood', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get high priority risks
     */
    public function getHighPriorityRisks(?int $projectId = null): array
    {
        $query = $this->where('impact_level', 'critical')
                     ->orWhere('impact_level', 'high')
                     ->whereIn('status', ['identified', 'active']);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('impact_level', 'DESC')
                    ->orderBy('likelihood', 'DESC')
                    ->findAll();
    }
    
    /**
     * Calculate risk score
     */
    public function calculateRiskScore(string $impactLevel, string $likelihood): int
    {
        $impactScores = [
            'low' => 1,
            'medium' => 2,
            'high' => 3,
            'critical' => 4
        ];
        
        $likelihoodScores = [
            'low' => 1,
            'medium' => 2,
            'high' => 3
        ];
        
        return ($impactScores[$impactLevel] ?? 1) * ($likelihoodScores[$likelihood] ?? 1);
    }
    
    /**
     * Get risk matrix
     */
    public function getRiskMatrix(int $projectId): array
    {
        $risks = $this->getByProject($projectId);
        $matrix = [];
        
        foreach ($risks as $risk) {
            $score = $this->calculateRiskScore($risk['impact_level'], $risk['likelihood']);
            $risk['risk_score'] = $score;
            
            if (!isset($matrix[$risk['impact_level']])) {
                $matrix[$risk['impact_level']] = [];
            }
            
            if (!isset($matrix[$risk['impact_level']][$risk['likelihood']])) {
                $matrix[$risk['impact_level']][$risk['likelihood']] = [];
            }
            
            $matrix[$risk['impact_level']][$risk['likelihood']][] = $risk;
        }
        
        return $matrix;
    }
    
    /**
     * Update risk status
     */
    public function updateStatus(int $riskId, string $status, ?int $updatedBy = null): bool
    {
        return $this->update($riskId, [
            'status' => $status,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Update mitigation strategy
     */
    public function updateMitigationStrategy(int $riskId, string $strategy, ?int $updatedBy = null): bool
    {
        return $this->update($riskId, [
            'mitigation_strategy' => $strategy,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Get risk statistics
     */
    public function getRiskStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total risks
        $stats['total_risks'] = $query->countAllResults();
        
        // Risks by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Risks by impact level
        $impactCounts = $this->select('impact_level, COUNT(*) as count')
                            ->groupBy('impact_level')
                            ->findAll();
        
        $stats['by_impact'] = array_column($impactCounts, 'count', 'impact_level');
        
        // Risks by likelihood
        $likelihoodCounts = $this->select('likelihood, COUNT(*) as count')
                                ->groupBy('likelihood')
                                ->findAll();
        
        $stats['by_likelihood'] = array_column($likelihoodCounts, 'count', 'likelihood');
        
        // Active risks count
        $activeQuery = $this->whereIn('status', ['identified', 'active']);
        if ($projectId) {
            $activeQuery = $activeQuery->where('project_id', $projectId);
        }
        $stats['active_risks'] = $activeQuery->countAllResults();
        
        // High priority risks count
        $highPriorityQuery = $this->whereIn('impact_level', ['high', 'critical'])
                                 ->whereIn('status', ['identified', 'active']);
        if ($projectId) {
            $highPriorityQuery = $highPriorityQuery->where('project_id', $projectId);
        }
        $stats['high_priority_risks'] = $highPriorityQuery->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get risks requiring attention
     */
    public function getRisksRequiringAttention(?int $projectId = null): array
    {
        $query = $this->whereIn('impact_level', ['high', 'critical'])
                     ->where('status', 'identified');
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('impact_level', 'DESC')
                    ->orderBy('likelihood', 'DESC')
                    ->findAll();
    }
    
    /**
     * Search risks
     */
    public function searchRisks(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->groupStart()
                     ->like('risk_description', $searchTerm)
                     ->orLike('mitigation_strategy', $searchTerm)
                     ->groupEnd();
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Close risk
     */
    public function closeRisk(int $riskId, ?int $updatedBy = null): bool
    {
        return $this->updateStatus($riskId, 'closed', $updatedBy);
    }
    
    /**
     * Reopen risk
     */
    public function reopenRisk(int $riskId, ?int $updatedBy = null): bool
    {
        return $this->updateStatus($riskId, 'active', $updatedBy);
    }
}
