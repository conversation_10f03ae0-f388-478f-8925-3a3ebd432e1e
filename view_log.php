<?php
/**
 * Simple log viewer for debugging
 */

$logFile = $_GET['file'] ?? '';
$logPath = 'writable/logs/' . $logFile;

echo "<h1>Log Viewer</h1>";

if (empty($logFile)) {
    echo "<p>No log file specified.</p>";
    echo "<p><a href='debug_auth_filter.php'>Back to Debug</a></p>";
    exit;
}

if (!file_exists($logPath)) {
    echo "<p style='color: red;'>Log file not found: $logPath</p>";
    echo "<p><a href='debug_auth_filter.php'>Back to Debug</a></p>";
    exit;
}

echo "<h2>File: $logFile</h2>";
echo "<p><a href='debug_auth_filter.php'>Back to Debug</a></p>";

// Read last 100 lines of the log file
$lines = file($logPath);
$totalLines = count($lines);
$startLine = max(0, $totalLines - 100);

echo "<p>Showing last 100 lines (lines " . ($startLine + 1) . " to $totalLines):</p>";

echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 600px; overflow-y: scroll;'>";
for ($i = $startLine; $i < $totalLines; $i++) {
    $line = htmlspecialchars($lines[$i]);
    
    // Highlight different log levels
    if (strpos($line, 'ERROR') !== false) {
        echo "<span style='color: red; font-weight: bold;'>$line</span>";
    } elseif (strpos($line, 'DEBUG') !== false) {
        echo "<span style='color: blue;'>$line</span>";
    } elseif (strpos($line, 'INFO') !== false) {
        echo "<span style='color: green;'>$line</span>";
    } else {
        echo $line;
    }
}
echo "</pre>";

echo "<p><a href='debug_auth_filter.php'>Back to Debug</a></p>";
?>
