Based on the PROMIS system documentation, here's the comprehensive system process design:

# PROMIS System Process Design

## A. Authentication & Access

### Feature: User Login
**Process diagram:**
```
User accesses main login → Enter credentials → Validate credentials → 
├─ [Success & is_project_officer=1] → Redirect to Project Monitoring Portal
├─ [Success & is_project_officer=0] → Redirect to Admin Portal
└─ [Fail] → Return to login with error message
```

### Feature: Organization Admin Login
**Process diagram:**
```
User accesses /organization/login → Enter credentials → Validate credentials →
├─ [Success] → Redirect to Admin Portal Dashboard
└─ [Fail] → Return to login with error message
```

### Feature: Dakoii Portal Login
**Process diagram:**
```
Developer accesses /dakoii → Enter credentials → Validate credentials →
├─ [Success] → Redirect to Dakoii Dashboard
└─ [Fail] → Return to login with error message
```

### Feature: Logout
**Process diagram:**
```
User clicks logout → Destroy session → Clear cookies → Log audit trail → Redirect to login page
```

## B. User Account Management

### Feature: Create Organization Admin (Dakoii Portal)
**Process diagram:**
```
Dakoii Dashboard → Organizations List → View Organization → Administrators List → 
Add Administrator → Fill form (name, email) → Generate unique admin code → 
├─ [Success] → Insert to DB → Send activation email → Redirect to admin profile
└─ [Error] → Return to form with error message
```

### Feature: Admin Account Activation
**Process diagram:**
```
Admin receives activation email → Click activation link → Validate token →
├─ [Valid] → Set is_activated=1 → Generate 4-digit temp password → Email password → Show success page
└─ [Invalid/Expired] → Show error message with resend option
```

### Feature: Create User (Admin Portal)
**Process diagram:**
```
Admin Dashboard → Manage Users → Add User → Two-step wizard →
Step 1: Account Details → Step 2: Role & Permissions →
├─ [Success] → Insert to DB → Send activation email → Redirect to user list
└─ [Error] → Return to current step with error message
```

### Feature: Reset User Password
**Process diagram:**
```
Users List → Select User → View Profile → Click Reset Password →
Confirm action → Generate time-bound reset token → Send reset email →
User clicks link → Validate token →
├─ [Valid] → Enter new password → Update DB → Login with new password
└─ [Invalid] → Show error with option to request new link
```

## C. Organization Management (Dakoii Portal)

### Feature: Create Organization
**Process diagram:**
```
Dakoii Dashboard → Organizations List → Add Organization →
Fill form (name, required fields) → Auto-generate 5-digit org code →
├─ [Success] → Insert to DB (license=paid, is_active=1) → Redirect to organization profile
└─ [Error] → Return to form with error message
```

### Feature: Update Organization
**Process diagram:**
```
Organizations List → View Organization → Edit (via modal) →
├─ Upload Logo/Wallpaper → Validate file → Save to storage → Update DB
├─ Change License Status → Confirm action → Update DB → Log audit
└─ Toggle Status → Confirm action → Update DB → Log audit
```

### Feature: Manage Organization Images
**Process diagram:**
```
Organization Profile → Organization Images → Add Images (max 5) →
Upload files → Validate → Save to storage → Update organization_images table →
Set sort_order → Display in gallery
```

## D. Project Management (Admin Portal)

### Feature: Create Project
**Process diagram:**
```
Admin Dashboard → Manage Projects → Add New Project →
Fill required fields → Generate project code (ID + Year) →
├─ [Success] → Insert to DB → Redirect to View Project Profile
└─ [Error] → Return to form with validation errors
```

### Feature: Manage Project Contractors
**Process diagram:**
```
Project Profile → Project Contractors → Assign Contractor →
Select from available contractors → Set role/service →
├─ [Assign] → Create relationship → Update project
└─ [Remove] → Set is_active=0 → Enter removal reason (required) → Update DB
```

### Feature: Manage Project Officers
**Process diagram:**
```
Project Profile → Project Officers → Assign Officer →
Select from users where is_project_officer=1 → Set role (lead/certifier/support) →
├─ [First officer] → Auto-set as lead → Create assignment
├─ [Additional] → Select role → Create assignment
└─ [Remove] → Mark removed → Enter reason → Update DB
```

### Feature: Manage Project Budget
**Process diagram:**
```
Project Profile → Project Finances → Budget Items tab →
├─ [Add] → Enter budget details → Save to DB
├─ [Edit] → Update budget item → Save changes
└─ [Remove] → Set status=removed → Apply strikethrough CSS → Keep in list
```

### Feature: Manage Project Expenses
**Process diagram:**
```
Project Profile → Project Finances → Expenses tab →
Add Expense → Fill payment details → Upload supporting documents →
├─ [Success] → Save expense record → Link uploaded files → Update totals
└─ [Error] → Return to form with validation errors
```

## E. Project Phases & Milestones

### Feature: Manage Project Phases
**Process diagram:**
```
Project Profile → Project Phases & Milestones → Add Phase (modal) →
Enter phase details → Set order/timeline →
├─ [Success] → Insert phase → Refresh phase list
└─ [Error] → Show validation in modal
```

### Feature: Manage Milestones
**Process diagram:**
```
Phases List → Select Phase → Add Milestone (modal) →
Enter milestone details → Set target date (from Gantt) →
├─ [Success] → Insert milestone → Link to phase → Display in list
└─ [Error] → Show validation in modal
```

## F. Project Monitoring (Monitoring Portal)

### Feature: Post Milestone Update
**Process diagram:**
```
My Projects → Select Project → Project Monitoring → Create Post →
Select post type (Image/Event/Feedback/Risk/Payment/File/GPS) →
Fill post details → Submit →
├─ [Success] → Save to DB → Set pending approval → Notify admin
└─ [Error] → Show error message
```

### Feature: Mark Milestone Complete
**Process diagram:**
```
Project Monitoring → Milestone List → Mark as Completed →
Add completion evidence → Submit →
Save completion request → Pending admin approval →
Admin approves → Pending M&E rating → M&E rates → Milestone completed
```

## G. Approval Workflows (Admin Portal)

### Feature: Approve Monitoring Posts
**Process diagram:**
```
Admin Dashboard → Pending Approvals → Monitoring Posts →
Review post details → 
├─ [Approve] → Update status → Publish post → Notify officer
├─ [Reject] → Add rejection reason → Notify officer
└─ [Request Revision] → Add comments → Notify officer
```

### Feature: Milestone Completion Approval
**Process diagram:**
```
Pending Approvals → Milestone Completions → Review evidence →
├─ [Approve] → Update status → Forward to M&E → 
│   └─ M&E rates → Check if all milestones complete →
│       └─ [All complete] → Enable certificate generation
└─ [Reject] → Add reason → Notify officer
```

## H. Certificate Generation

### Feature: Generate Project Certificates
**Process diagram:**
```
Project Profile → [All milestones completed & M&E approved] →
Generate Certificates button appears → Click Generate →
Confirm generation (one-time only) →
├─ Generate contractor certificate → Save to storage
├─ Generate officer certificates (for each role) → Save to storage
└─ Log certificate generation → Update project status
```

## I. Contractor Management

### Feature: Create Contractor
**Process diagram:**
```
Admin Dashboard → Manage Contractors → Add Contractor →
Fill registration details → Set service categories →
├─ [Success] → Insert to DB → Redirect to contractor profile
└─ [Error] → Return to form with validation errors
```

### Feature: Manage Contractor Documents
**Process diagram:**
```
Contractor Profile → Document Vault → Upload Document →
Select document type → Set expiration date → Upload file →
├─ [Success] → Save document → Set expiration tracking
└─ [Expired] → Auto-flag document → Show in compliance alerts
```

### Feature: Update Client Satisfaction
**Process diagram:**
```
Contractor Profile → Project History → Select Project →
Set satisfaction flag (positive/neutral/negative) →
Add comments → Save to project_contractors relationship
```

## J. Government Structure Management (Dakoii Portal)

### Feature: Create Government Unit
**Process diagram:**
```
Gov Structure → Select Level → Create Unit (modal) →
Select parent (if applicable) → Load GeoJSON dropdown →
Enter name, code → Set map defaults →
├─ [Success] → Insert to appropriate table → Update hierarchy chart
└─ [Error] → Show validation in modal
```

### Feature: Generate Hierarchy Chart
**Process diagram:**
```
Gov Structure → View any level → 
Load current unit → Recursively fetch children →
Build tree structure → Render interactive chart →
Click on unit → Navigate to unit's children list
```

## K. Reporting

### Feature: Generate Reports
**Process diagram:**
```
Admin Dashboard → Manage Reports → Select Report Type →
├─ [Project Report] → Select project → Choose format → Generate → Download/View
├─ [Financial Report] → Set parameters → Generate → Export (PDF/CSV/Excel)
├─ [M&E Report] → Select indicators → Compare baseline/target/actual → Generate
└─ [Custom Report] → Build query → Preview → Generate → Export
```

## L. Data Management

### Feature: Data Import
**Process diagram:**
```
Module → Import Data → Upload CSV/Excel →
Validate format → Preview data → Map fields →
├─ [Valid] → Confirm import → Process in batches → Show results
└─ [Invalid] → Show error report → Allow correction → Retry
```

### Feature: Recycle Bin
**Process diagram:**
```
Dashboard → Recycle Bin → View deleted items →
├─ [Restore] → Confirm → Set deleted_at=NULL → Restore to original location
└─ [30 days passed] → Auto-permanent delete → Remove from DB
```

## M. Audit Trail

### Feature: System Audit Logging
**Process diagram:**
```
Any CRUD operation → Capture action details →
Log to user_audit_log (user_id, action, table, record_id, timestamp) →
├─ Login/Logout → Log authentication events
├─ Data Changes → Log old/new values
├─ Exports → Log data access
├─ Permission Changes → Log security changes
└─ Certificate Generation → Log one-time events
```

### Feature: View Audit Logs
**Process diagram:**
```
Dashboard → System Settings → Security Logs →
Apply filters (user, action, date range) →
View results → Export to CSV for compliance
```

This comprehensive process design covers all major workflows in the PROMIS system across all three portals (Dakoii, Admin, and Project Monitoring), showing the complete flow from user actions to system responses, including error handling and success paths.