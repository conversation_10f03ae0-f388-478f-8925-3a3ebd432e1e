Based on the PROMIS system documentation, here's the comprehensive system process design:

# PROMIS System Process Design

## System Overview
PROMIS is a multi-portal project management system with four distinct interfaces:
- **Dakoii Portal**: Super admin interface for system-wide management
- **Admin Portal**: Organization-specific project and user management
- **Monitoring Portal**: Read-only project monitoring and evaluation
- **Landing Pages**: Public-facing information and registration

Each portal has its own authentication system, dashboard interface, and user roles. Users are redirected to their appropriate portal dashboard upon successful login.

## A. Authentication & Access

### Feature: Organization User Login (Admin/Monitoring Portal)
**Process diagram:**
```
User accesses /admin/login or /monitor/login → Enter credentials → Validate credentials →
Check user organization and role →
├─ [Success & is_project_officer=1] → Redirect to Monitoring Portal Dashboard
├─ [Success & is_project_officer=0] → Redirect to Admin Portal Dashboard
├─ [Success & role-based access] → Redirect to appropriate portal dashboard
└─ [Fail] → Return to login with error message
```

**Key Points:**
- Each portal (Admin/Monitoring) has separate login endpoints
- Users are redirected to their specific portal dashboard, not a shared interface
- Role-based redirection ensures users access appropriate functionality
- Session management is portal-specific with different session prefixes

### Feature: Dakoii Super Admin Login
**Process diagram:**
```
Super admin accesses /dakoii → Enter credentials → Validate against dakoii_users table →
├─ [Success & is_activated=1] → Create dakoii session → Redirect to Dakoii Dashboard
└─ [Fail] → Return to login with error message
```

**Key Points:**
- Completely separate authentication system from organization users
- Uses DakoiiUserModel and dakoii_users table
- Session timeout: 8 hours with remember me option
- Audit logging for all authentication events

### Feature: Portal-Specific Logout
**Process diagram:**
```
User clicks logout → Identify portal context → Destroy portal-specific session →
Clear remember me cookies → Log audit trail with portal context →
Redirect to appropriate portal login page
```

**Key Points:**
- Each portal maintains separate session data
- Logout redirects to the specific portal's login page
- Audit trail includes portal context (dakoii, admin, monitor)

## B. User Account Management

### Feature: Organization User Registration (Public)
**Process diagram:**
```
Public user accesses registration → Fill organization details → Submit →
Validate organization data → Generate org_code → Create organization record →
Create admin user account → Send activation email →
├─ [Success] → Show registration success page with next steps
└─ [Error] → Return to form with validation errors
```

**Key Points:**
- Public registration creates both organization and admin user
- Two-step activation: email activation then temporary password
- Organization gets default settings (license_status, location_locks)
- Admin user gets default role permissions

### Feature: Create Organization Admin (Dakoii Portal)
**Process diagram:**
```
Dakoii Dashboard → Organizations List → View Organization → Administrators List →
Add Administrator → Fill form (name, email, role) → Generate unique user_code →
Validate organization association →
├─ [Success] → Insert to users table with organization_id → Send activation email →
│   Log audit trail → Redirect to admin profile
└─ [Error] → Return to form with validation errors
```

**Key Points:**
- Creates users in the main 'users' table, not dakoii_users
- Associates user with specific organization via organization_id
- Uses UserModel, not DakoiiUserModel
- Role options: admin, moderator, editor, user

### Feature: Organization User Account Activation
**Process diagram:**
```
User receives activation email → Click activation link → Validate token →
├─ [Valid] → Set is_activated=1 → Generate 4-digit numeric temp password →
│   Email temp password → Show success page with login instructions
└─ [Invalid/Expired] → Show error message with resend activation option
```

**Key Points:**
- Two-step process: activation first, then temporary password
- Temporary passwords are 4-digit numbers (not complex passwords)
- Email templates use professional styling with PROMIS branding
- Activation links have expiration time for security

### Feature: Create Organization User (Admin Portal)
**Process diagram:**
```
Admin Portal Dashboard → Manage Users → Add User → Fill user details form →
Set role (admin/moderator/editor/user) → Set permissions (is_supervisor, is_project_officer, is_mon_eval) →
Validate within organization context →
├─ [Success] → Insert to users table → Send activation email → Redirect to user list
└─ [Error] → Return to form with validation errors
```

**Key Points:**
- Admin can only create users within their own organization
- Role-based permissions control access to different portal features
- Standard CodeIgniter 4 form submission (no AJAX)
- Audit logging tracks user creation by admin

### Feature: Reset User Password
**Process diagram:**
```
Users List → Select User → View Profile → Click Reset Password →
Confirm action → Generate time-bound reset token → Send reset email →
User clicks link → Validate token →
├─ [Valid] → Enter new password → Update DB → Login with new password
└─ [Invalid] → Show error with option to request new link
```

## C. Organization Management (Dakoii Portal)

### Feature: Create Organization
**Process diagram:**
```
Dakoii Dashboard → Organizations List → Add Organization →
Fill form (name, required fields) → Auto-generate 5-digit org code →
├─ [Success] → Insert to DB (license=paid, is_active=1) → Redirect to organization profile
└─ [Error] → Return to form with error message
```

### Feature: Update Organization (Dakoii Portal)
**Process diagram:**
```
Organizations List → View Organization → Edit Organization →
├─ Upload Logo/Wallpaper → Validate file (15MB max, image types) →
│   Save to public/uploads/organizations/{org_id}/ → Update DB with 'public/' prefix
├─ Change License Status → Confirm action → Update DB → Log audit trail
├─ Toggle Active Status → Confirm action → Update DB → Log audit trail
├─ Update Contact Info → Validate → Update DB → Log audit trail
└─ Modify Location Locks → Update country_lock, province_lock, district_lock, llg_lock
```

**Key Points:**
- File uploads always use 'public/' prefix for database storage
- Files stored in public/uploads/organizations/{org_id}/ structure
- All changes logged to unified audit trail
- Location locks control geographical restrictions for organization

### Feature: Manage Organization Images (Dakoii Portal)
**Process diagram:**
```
Organization Profile → Organization Images → Upload Images →
Validate files (15MB max, image types only) →
Save to public/uploads/organizations/{org_id}/images/ →
Insert to organization_images table with 'public/' prefix →
Set sort_order → Display in gallery with management options
```

**Key Points:**
- Uses OrganizationImageModel for database operations
- Supports multiple image uploads in single operation
- Images displayed in sortable gallery interface
- Delete operations use soft delete (deleted_at field)

## D. Project Management (Admin Portal)

### Feature: Create Project
**Process diagram:**
```
Admin Dashboard → Manage Projects → Add New Project →
Fill required fields → Generate project code (ID + Year) →
├─ [Success] → Insert to DB → Redirect to View Project Profile
└─ [Error] → Return to form with validation errors
```

### Feature: Manage Project Contractors
**Process diagram:**
```
Project Profile → Project Contractors → Assign Contractor →
Select from available contractors → Set role/service →
├─ [Assign] → Create relationship → Update project
└─ [Remove] → Set is_active=0 → Enter removal reason (required) → Update DB
```

### Feature: Manage Project Officers
**Process diagram:**
```
Project Profile → Project Officers → Assign Officer →
Select from users where is_project_officer=1 → Set role (lead/certifier/support) →
├─ [First officer] → Auto-set as lead → Create assignment
├─ [Additional] → Select role → Create assignment
└─ [Remove] → Mark removed → Enter reason → Update DB
```

### Feature: Manage Project Budget
**Process diagram:**
```
Project Profile → Project Finances → Budget Items tab →
├─ [Add] → Enter budget details → Save to DB
├─ [Edit] → Update budget item → Save changes
└─ [Remove] → Set status=removed → Apply strikethrough CSS → Keep in list
```

### Feature: Manage Project Expenses
**Process diagram:**
```
Project Profile → Project Finances → Expenses tab →
Add Expense → Fill payment details → Upload supporting documents →
├─ [Success] → Save expense record → Link uploaded files → Update totals
└─ [Error] → Return to form with validation errors
```

## E. Project Phases & Milestones

### Feature: Manage Project Phases
**Process diagram:**
```
Project Profile → Project Phases & Milestones → Add Phase (modal) →
Enter phase details → Set order/timeline →
├─ [Success] → Insert phase → Refresh phase list
└─ [Error] → Show validation in modal
```

### Feature: Manage Milestones
**Process diagram:**
```
Phases List → Select Phase → Add Milestone (modal) →
Enter milestone details → Set target date (from Gantt) →
├─ [Success] → Insert milestone → Link to phase → Display in list
└─ [Error] → Show validation in modal
```

## F. Project Monitoring (Monitoring Portal)

**Portal Context:**
- Separate dashboard interface from Admin Portal
- Read-only access to project data with monitoring capabilities
- Users with is_project_officer=1 are redirected here upon login
- Focus on progress tracking, evidence submission, and field updates

### Feature: Access Project Monitoring Dashboard
**Process diagram:**
```
Project Officer Login → Validate credentials → Check is_project_officer=1 →
Redirect to Monitoring Portal Dashboard → Display assigned projects →
Show pending tasks, recent updates, and monitoring alerts
```

**Key Points:**
- Completely separate dashboard from Admin Portal
- Shows only projects where user is assigned as project officer
- Dashboard displays monitoring-specific widgets and alerts
- Read-only view of project data with monitoring actions

### Feature: Submit Milestone Progress Update
**Process diagram:**
```
Monitoring Dashboard → My Assigned Projects → Select Project →
View Project Monitoring → Select Milestone → Create Progress Update →
Choose update type (Progress Report/Evidence Upload/Issue Report/GPS Update) →
Fill update details → Upload supporting files (if applicable) →
├─ [Success] → Save to monitoring_updates table → Set status=pending →
│   Notify admin for approval → Return to project monitoring
└─ [Error] → Show validation errors → Return to form
```

**Key Points:**
- Updates require admin approval before becoming visible
- File uploads follow same security pattern (public/ prefix)
- GPS updates can include location coordinates
- All updates linked to specific milestones and projects

### Feature: Mark Milestone as Complete
**Process diagram:**
```
Project Monitoring → Milestone List → Select Milestone → Mark Complete →
Upload completion evidence (photos, documents) → Add completion notes →
Submit completion request →
├─ [Success] → Save completion request → Set status=pending_approval →
│   Notify admin → Notify M&E officer → Return to milestone list
└─ [Error] → Show validation errors → Return to form
```

**Key Points:**
- Completion requires evidence upload (mandatory)
- Three-stage approval: Officer → Admin → M&E
- M&E officer provides final rating/approval
- Milestone only marked complete after all approvals

## G. Approval Workflows (Admin Portal)

### Feature: Approve Monitoring Posts
**Process diagram:**
```
Admin Dashboard → Pending Approvals → Monitoring Posts →
Review post details → 
├─ [Approve] → Update status → Publish post → Notify officer
├─ [Reject] → Add rejection reason → Notify officer
└─ [Request Revision] → Add comments → Notify officer
```

### Feature: Milestone Completion Approval
**Process diagram:**
```
Pending Approvals → Milestone Completions → Review evidence →
├─ [Approve] → Update status → Forward to M&E → 
│   └─ M&E rates → Check if all milestones complete →
│       └─ [All complete] → Enable certificate generation
└─ [Reject] → Add reason → Notify officer
```

## H. Certificate Generation

### Feature: Generate Project Certificates
**Process diagram:**
```
Project Profile → [All milestones completed & M&E approved] →
Generate Certificates button appears → Click Generate →
Confirm generation (one-time only) →
├─ Generate contractor certificate → Save to storage
├─ Generate officer certificates (for each role) → Save to storage
└─ Log certificate generation → Update project status
```

## I. Contractor Management

### Feature: Create Contractor
**Process diagram:**
```
Admin Dashboard → Manage Contractors → Add Contractor →
Fill registration details → Set service categories →
├─ [Success] → Insert to DB → Redirect to contractor profile
└─ [Error] → Return to form with validation errors
```

### Feature: Manage Contractor Documents
**Process diagram:**
```
Contractor Profile → Document Vault → Upload Document →
Select document type → Set expiration date → Upload file →
├─ [Success] → Save document → Set expiration tracking
└─ [Expired] → Auto-flag document → Show in compliance alerts
```

### Feature: Update Client Satisfaction
**Process diagram:**
```
Contractor Profile → Project History → Select Project →
Set satisfaction flag (positive/neutral/negative) →
Add comments → Save to project_contractors relationship
```

## J. Government Structure Management (Dakoii Portal)

### Feature: Create Government Unit (Dakoii Portal)
**Process diagram:**
```
Government Structure → Select Level (Country/Province/District/LLG) →
Create Unit → Load GeoJSON dropdown for parent selection →
Select parent unit (if applicable) → Enter unit details (name, code) →
Validate hierarchy constraints →
├─ [Success] → Insert to appropriate table (countries/provinces/districts/llgs) →
│   Update hierarchy relationships → Log audit trail → Refresh structure view
└─ [Error] → Show validation errors in modal → Return to form
```

**Key Points:**
- Uses GeoJSON files from public/map_jsons/ for dropdown data
- Hierarchical validation ensures proper parent-child relationships
- Each level has its own model (CountryModel, ProvinceModel, etc.)
- MapJsonHelper assists with loading geographical data

### Feature: Government Structure Visualization
**Process diagram:**
```
Government Structure → Select View Type (Chart/Map/List) →
├─ [Chart View] → Load hierarchical data → Build tree structure →
│   Render interactive chart → Enable drill-down navigation
├─ [Map View] → Load GeoJSON data → Render geographical boundaries →
│   Enable click-to-navigate → Show unit details on selection
└─ [List View] → Display tabular data → Enable filtering and search →
    Provide CRUD operations for each unit
```

**Key Points:**
- Multiple visualization options for different use cases
- Chart view shows hierarchical relationships
- Map view integrates with GeoJSON boundary data
- All views support navigation and management operations

## K. Reporting

### Feature: Generate Reports
**Process diagram:**
```
Admin Dashboard → Manage Reports → Select Report Type →
├─ [Project Report] → Select project → Choose format → Generate → Download/View
├─ [Financial Report] → Set parameters → Generate → Export (PDF/CSV/Excel)
├─ [M&E Report] → Select indicators → Compare baseline/target/actual → Generate
└─ [Custom Report] → Build query → Preview → Generate → Export
```

## L. Data Management

### Feature: Data Import
**Process diagram:**
```
Module → Import Data → Upload CSV/Excel →
Validate format → Preview data → Map fields →
├─ [Valid] → Confirm import → Process in batches → Show results
└─ [Invalid] → Show error report → Allow correction → Retry
```

### Feature: Recycle Bin
**Process diagram:**
```
Dashboard → Recycle Bin → View deleted items →
├─ [Restore] → Confirm → Set deleted_at=NULL → Restore to original location
└─ [30 days passed] → Auto-permanent delete → Remove from DB
```

## M. Audit Trail

### Feature: Unified Audit Trail System
**Process diagram:**
```
Any CRUD operation across all portals → Detect portal context →
Capture comprehensive action details →
Log to unified audit_logs table with portal context →
├─ Authentication Events → Log login/logout with portal and user details
├─ Data Modifications → Log old/new values with change context
├─ File Operations → Log uploads/downloads with file paths
├─ Permission Changes → Log role/access modifications
├─ System Events → Log certificate generation, exports, etc.
└─ Cross-Portal Activities → Log organization/project access across portals
```

**Key Points:**
- Single audit table serves all portals (Dakoii, Admin, Monitor)
- Uses Auditable trait in BaseModel for automatic logging
- Portal context automatically detected and logged
- Comprehensive tracking includes user, organization, and system context

### Feature: View Audit Logs (Dakoii Portal)
**Process diagram:**
```
Dakoii Dashboard → Audit Trail → Apply filters →
├─ Filter by Portal → Show Dakoii/Admin/Monitor activities
├─ Filter by Organization → Show specific organization activities
├─ Filter by User → Show individual user activities
├─ Filter by Action Type → Show specific operation types
└─ Filter by Date Range → Show time-bounded activities
→ View detailed results → Export to CSV for compliance reporting
```

**Key Points:**
- Dakoii portal has full access to all audit data
- Organization admins see only their organization's audit trail
- Comprehensive filtering and search capabilities
- Export functionality for compliance and reporting needs

## N. System Integration Points

### Feature: Cross-Portal Data Flow
**Process diagram:**
```
Dakoii Portal → Creates organizations and system users →
Admin Portal → Uses organization context for project management →
Monitor Portal → Accesses project data for monitoring →
All Portals → Contribute to unified audit trail
```

**Key Points:**
- Dakoii portal provides foundational data for other portals
- Organization context isolates data between different organizations
- Government structure data is shared across all portals
- Audit trail provides system-wide activity tracking

This comprehensive process design covers all major workflows in the PROMIS system across all four interfaces (Dakoii, Admin, Monitoring, and Landing), emphasizing the separate dashboard interfaces and portal-specific functionality while showing complete user flows from authentication to task completion.