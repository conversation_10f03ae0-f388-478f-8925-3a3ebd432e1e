<?php
/**
 * Debug script to test authentication filter and session
 */

// Start session to check session data
session_start();

echo "<h1>Authentication Filter Debug</h1>";

// Check session data
echo "<h2>Session Data</h2>";
if (empty($_SESSION)) {
    echo "<p style='color: red;'>No session data found!</p>";
    echo "<p>You need to log in first: <a href='http://localhost/promis_two/dakoii'>Login to Dakoii</a></p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Key</th><th>Value</th></tr>";
    foreach ($_SESSION as $key => $value) {
        echo "<tr><td>$key</td><td>" . (is_array($value) ? print_r($value, true) : $value) . "</td></tr>";
    }
    echo "</table>";
}

// Check specific Dakoii session variables
echo "<h2>Dakoii Authentication Check</h2>";
$dakoiiLoggedIn = isset($_SESSION['dakoii_logged_in']) ? $_SESSION['dakoii_logged_in'] : 'Not set';
$dakoiiUserId = isset($_SESSION['dakoii_user_id']) ? $_SESSION['dakoii_user_id'] : 'Not set';

echo "<p><strong>dakoii_logged_in:</strong> $dakoiiLoggedIn</p>";
echo "<p><strong>dakoii_user_id:</strong> $dakoiiUserId</p>";

if ($dakoiiLoggedIn && $dakoiiUserId) {
    echo "<p style='color: green;'>✓ Authentication looks good!</p>";
} else {
    echo "<p style='color: red;'>✗ Authentication missing!</p>";
}

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    $pdo = new PDO("mysql:host=127.0.0.1;dbname=promis_two_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Check if dakoii_users table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM dakoii_users WHERE deleted_at IS NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Dakoii users in database: {$result['count']}</p>";
    
    if ($result['count'] == 0) {
        echo "<p style='color: orange;'>No Dakoii users found. You may need to run the seeder.</p>";
        echo "<p>Run: <code>php spark db:seed DakoiiUserSeeder</code></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

// Test form submission simulation
echo "<h2>Form Submission Test</h2>";
echo "<p>This form will test if the POST route is working:</p>";

echo '<form method="POST" action="http://localhost/promis_two/dakoii/government/countries/create" target="_blank">';
echo '<input type="hidden" name="csrf_test_name" value="test_token">';
echo '<table>';
echo '<tr><td>Name:</td><td><input type="text" name="name" value="Test Country Debug" required></td></tr>';
echo '<tr><td>ISO2:</td><td><input type="text" name="iso2" value="TD" maxlength="2" required></td></tr>';
echo '<tr><td>ISO3:</td><td><input type="text" name="iso3" value="TDB" maxlength="3" required></td></tr>';
echo '<tr><td>Map Center GPS:</td><td><input type="text" name="map_centre_gps" value="-6.314993,143.95555"></td></tr>';
echo '<tr><td>Map Zoom:</td><td><input type="number" name="map_zoom" value="6" min="1" max="20"></td></tr>';
echo '</table>';
echo '<p><input type="submit" value="Test Submit (will open in new tab)"></p>';
echo '</form>';

// Check CodeIgniter logs
echo "<h2>Log File Check</h2>";
$logPath = 'writable/logs/';
if (is_dir($logPath)) {
    $logFiles = glob($logPath . '*.log');
    if (!empty($logFiles)) {
        $latestLog = max($logFiles);
        echo "<p>Latest log file: " . basename($latestLog) . "</p>";
        echo "<p><a href='view_log.php?file=" . basename($latestLog) . "'>View Latest Log</a></p>";
    } else {
        echo "<p>No log files found in $logPath</p>";
    }
} else {
    echo "<p style='color: red;'>Log directory not found: $logPath</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>If not logged in: <a href='http://localhost/promis_two/dakoii'>Login to Dakoii Portal</a></li>";
echo "<li>If logged in: <a href='http://localhost/promis_two/dakoii/government/countries/create'>Test Create Form</a></li>";
echo "<li>Check the browser developer tools Network tab when submitting the form</li>";
echo "<li>Check the CodeIgniter logs for debug messages</li>";
echo "</ol>";
?>
