{"systemProfile": {"name": "PROMIS - Project Management Information Systems", "version": "2.0", "description": "A comprehensive multi-portal project management system with organization management, government structure tracking, and audit capabilities", "baseUrl": "http://localhost/promis_two/", "environment": "XAMPP Development", "architecture": {"framework": "CodeIgniter 4", "phpVersion": "^8.1", "database": "MySQL", "structure": "Multi-Portal Architecture", "patterns": ["MVC (Model-View-Controller)", "RESTful API Design", "Repository Pattern via Models", "Service Layer Pattern", "Audit Trail Pattern", "Template Inheritance"]}, "portals": {"dakoii": {"name": "Dakoii Super Admin Portal", "description": "Master administration portal for system-wide management", "baseRoute": "/dakoii", "authentication": "Da<PERSON>iiAuth<PERSON><PERSON><PERSON>", "template": "dakoii_portal_template.php", "theme": "dark-glassmorphic", "features": ["Organization Management", "System User Management", "Government Structure Management", "Audit Trail Monitoring", "Dashboard Analytics"]}, "admin": {"name": "Organization Admin Portal", "description": "Organization-specific administration for projects and users", "baseRoute": "/admin", "authentication": "AdminAuthFilter (planned)", "template": "promis_admin_template.php", "theme": "light-professional", "features": ["Project Management", "User Management", "Contractor Management", "Budget Management", "Reports & Analytics"]}, "monitor": {"name": "Project Monitoring Portal", "description": "Read-only monitoring and evaluation portal", "baseRoute": "/monitor", "authentication": "MonitorAuthFilter (planned)", "template": "promis_monitor_template.php (planned)", "theme": "light-minimal", "features": ["Project Monitoring", "Progress Tracking", "Report Generation", "Data Visualization"]}, "landing": {"name": "Public Landing Pages", "description": "Public-facing marketing and information pages", "baseRoute": "/", "authentication": "none", "template": "promis_landing_template.php", "theme": "light-professional-marketing", "features": ["System Information", "Feature Showcase", "Contact Information", "User Registration"]}}, "authentication": {"systems": {"dakoii": {"model": "DakoiiUserModel", "table": "dakoii_users", "fields": ["username", "email", "password_hash", "is_activated"], "sessionPrefix": "dakoii_", "filter": "Da<PERSON>iiAuth<PERSON><PERSON><PERSON>", "sessionTimeout": "8 hours", "features": ["remember_me", "password_reset", "account_activation"]}, "organization": {"model": "UserModel", "table": "users", "fields": ["username", "email", "password_hash", "organization_id", "role"], "sessionPrefix": "admin_", "filter": "AdminAuthFilter (planned)", "roles": ["admin", "moderator", "editor", "user"], "permissions": ["is_supervisor", "is_project_officer", "is_mon_eval"]}}, "passwordPolicy": {"minLength": 4, "requirements": "Basic length requirement", "tempPasswordLength": 4, "tempPasswordType": "numeric"}}, "database": {"structure": "Multi-tenant with organization isolation", "auditStrategy": "Unified audit trail across all portals", "softDeletes": true, "timestampFields": ["created_at", "updated_at", "deleted_at"], "auditFields": ["created_by", "updated_by", "deleted_by"], "coreEntities": {"organizations": {"table": "organizations", "model": "OrganizationModel", "features": ["branding", "location_locks", "contact_info", "social_media"]}, "projects": {"table": "projects", "model": "ProjectModel", "features": ["phases", "milestones", "budget", "contractors", "documents", "risks"]}, "users": {"tables": ["users", "dakoii_users"], "models": ["UserModel", "DakoiiUserModel"], "features": ["role_based_access", "organization_association"]}, "government": {"tables": ["countries", "provinces", "districts", "llgs"], "models": ["CountryModel", "ProvinceModel", "DistrictModel", "LlgModel"], "features": ["hierarchical_structure", "geojson_support"]}}}, "fileManagement": {"uploadPath": "public/uploads/", "maxFileSize": "15MB", "allowedTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"], "pathPrefix": "public/", "organizationStructure": "uploads/organizations/{org_id}/{type}/", "validation": "mime_type_and_size_validation"}, "emailSystem": {"provider": "SMTP", "host": "mail.dakoiims.com", "port": 465, "encryption": "SSL", "username": "<EMAIL>", "fromName": "PROMIS - Project Management System", "templates": ["activation_email.php", "admin_activation_email.php", "password_reset_email.php", "temp_password_email.php", "organization_admin_email.php"], "baseTemplate": "base_template.php"}, "designSystems": {"dakoii_theme": {"mode": "dark", "style": "glassmorphic-modern", "colors": {"background": {"primary": "#0A0E27", "secondary": "#151B3C", "tertiary": "#1E2749"}, "gradients": {"primary": "linear-gradient(135deg, #FF006E, #8338EC)", "secondary": "linear-gradient(45deg, #06FFA5, #00D4FF)", "accent": "linear-gradient(90deg, #FFB700, #FF006E)"}, "text": {"primary": "#FFFFFF", "secondary": "#B8BCC8", "tertiary": "#7C8091", "muted": "#4A4E5C"}}, "components": {"sidebar": "fixed, collapsible, glassmorphic", "cards": "translucent with blur effects", "charts": "dark theme with neon accents"}}, "admin_theme": {"mode": "light", "style": "professional-clean", "colors": {"background": {"primary": "#FFFFFF", "secondary": "#F8FAFC", "tertiary": "#F1F5F9"}, "gradients": {"primary": "linear-gradient(135deg, #3B82F6, #1D4ED8)", "secondary": "linear-gradient(135deg, #10B981, #059669)", "accent": "linear-gradient(135deg, #8B5CF6, #7C3AED)"}, "text": {"primary": "#1E293B", "secondary": "#475569", "tertiary": "#64748B", "muted": "#94A3B8"}}, "components": {"sidebar": "fixed, dark sidebar with light content", "cards": "clean white cards with subtle shadows", "forms": "professional styling with focus states"}}, "landing_theme": {"mode": "light", "style": "marketing-professional", "colors": {"background": {"primary": "#FFFFFF", "secondary": "#F8FAFC", "accent": "#EFF6FF"}, "gradients": {"hero": "linear-gradient(135deg, #667EEA 0%, #764BA2 100%)", "primary": "linear-gradient(135deg, #3B82F6, #1D4ED8)", "secondary": "linear-gradient(135deg, #10B981, #059669)"}}, "components": {"navbar": "fixed, translucent with blur", "hero": "gradient background with animations", "sections": "alternating backgrounds"}}}, "codingStandards": {"naming": {"controllers": "PascalCase with descriptive suffixes (e.g., DakoiiOrganizationController)", "models": "PascalCase with Model suffix (e.g., OrganizationModel)", "views": "snake_case with portal prefix (e.g., dakoii_organizations_list.php)", "routes": "kebab-case for URLs (e.g., /dakoii/organizations/create)", "database": "snake_case for tables and columns", "css_classes": "kebab-case with BEM methodology where applicable"}, "structure": {"controllers": "RESTful methods - separate GET and POST operations", "models": "Extend BaseModel for automatic audit logging", "views": "Template inheritance with section-based content", "routes": "Grouped by portal with appropriate filters"}, "security": {"authentication": "Session-based with portal-specific filters", "authorization": "Role-based access control", "csrf": "Available but not globally enabled", "input_validation": "Server-side validation with CodeIgniter rules", "file_uploads": "Type and size validation with secure paths"}}, "features": {"implemented": {"dakoii_portal": ["User authentication and session management", "Organization CRUD with file uploads", "Government structure management (Countries, Provinces, Districts, LLGs)", "System user management with roles", "Audit trail logging and viewing", "Dashboard with statistics", "Hierarchical government chart visualization", "Map-based government structure view"], "templates": ["Dakoii portal template (dark glassmorphic)", "Admin portal template (light professional)", "Landing page template (marketing)", "Email templates (professional styling)"], "infrastructure": ["Multi-portal routing system", "Unified audit trail across portals", "File upload management", "Email system configuration", "Session management with timeouts", "Soft delete functionality"]}, "planned": {"admin_portal": ["Project management CRUD", "Project phases and milestones", "Budget and expense tracking", "Contractor management", "Document management", "Risk assessment", "Progress reporting"], "monitor_portal": ["Read-only project monitoring", "Progress visualization", "Report generation", "Data export functionality"]}}, "integrations": {"geospatial": {"provider": "GeoJSON files", "location": "public/map_jsons/", "structure": "Hierarchical dropdowns (Countries → Provinces → Districts → LLGs)", "helper": "MapJsonHelper for data loading"}, "charts": {"library": "To be determined", "types": ["hierarchical", "statistical", "progress"], "styling": "Theme-aware (dark for <PERSON><PERSON><PERSON>, light for <PERSON><PERSON>)"}}, "developmentGuidelines": {"database": {"rule": "DO NOT create new tables or modify existing database structure", "approach": "Adapt Controllers, Views, and Models to match existing database", "conflict_resolution": "Change application code, not database schema"}, "forms": {"submission": "Standard CodeIgniter 4 form submission (NO AJAX)", "validation": "Server-side validation with redirect back on errors", "file_uploads": "Always add 'public/' prefix to database paths"}, "views": {"naming": "Prefix with folder name (e.g., admin_projects_list.php)", "location": "All views in Views/{portal_name}/ folder", "templates": "Extend appropriate portal template", "consistency": "Reference existing view interfaces for design patterns"}, "routes": {"approach": "RESTful with separate methods for GET and POST", "structure": "Never combine GET and POST in same method", "grouping": "Group by portal with authentication filters"}, "models": {"base": "Use existing models, do not create new ones", "extension": "Extend BaseModel for audit logging", "relationships": "Leverage existing model relationships"}}, "currentState": {"completion": {"dakoii_portal": "90% - Fully functional with all core features", "admin_portal": "10% - Template created, features planned", "monitor_portal": "0% - Not started", "landing_pages": "80% - Basic structure implemented"}, "database": {"status": "Established and stable", "tables": "All required tables exist with proper relationships", "data": "Sample data available for testing"}, "infrastructure": {"authentication": "Dakoii portal complete, others planned", "audit_system": "Fully implemented and functional", "file_management": "Working with proper security", "email_system": "Configured and tested"}}, "aiContextInformation": {"development_environment": {"server": "XAMPP", "base_url": "http://localhost/promis_two/", "php_version": "8.1+", "database": "MySQL via phpMyAdmin"}, "key_constraints": {"database_immutable": "Cannot modify existing database structure", "no_ajax_forms": "Use standard form submissions only", "file_path_prefix": "Always use 'public/' prefix for uploaded files", "template_consistency": "Maintain design consistency across portals", "restful_approach": "Separate GET and POST methods in controllers"}, "testing_approach": {"environment": "XAMPP local development", "url_structure": "http://localhost/promis_two/{portal}/{feature}", "authentication": "Use existing Dakoii portal for testing auth flows", "file_uploads": "Test with 15MB limit and supported image types"}, "common_patterns": {"controller_structure": "Authentication check → Model interaction → View rendering", "view_structure": "Extend template → Define sections → Render content", "model_usage": "Use existing models with BaseModel audit features", "error_handling": "Flash messages for user feedback", "success_flows": "Redirect with success messages"}, "portal_relationships": {"data_flow": "Dakoii creates organizations → Admin manages projects → Monitor views data", "user_hierarchy": "Dakoii users (system admin) → Organization users (project admin) → Monitor users (read-only)", "template_inheritance": "Each portal has distinct template with shared design principles"}}}}