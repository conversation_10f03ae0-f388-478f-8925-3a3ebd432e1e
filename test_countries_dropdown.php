<?php
/**
 * Test script to verify countries data for dropdown
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

echo "<h1>Countries Dropdown Test</h1>";

try {
    // Test CountryModel
    $countryModel = new \App\Models\CountryModel();
    
    echo "<h2>All Countries in Database</h2>";
    $allCountries = $countryModel->findAll();
    
    if (empty($allCountries)) {
        echo "<p style='color: red;'>No countries found in database!</p>";
        echo "<p>You may need to add some countries first.</p>";
        
        // Try to add a test country
        echo "<h3>Adding Test Country</h3>";
        $testCountry = [
            'iso2' => 'PG',
            'iso3' => 'PNG',
            'name' => 'Papua New Guinea',
            'map_centre_lat' => -6.314993,
            'map_centre_lng' => 143.95555,
            'map_zoom' => 6,
            'created_by' => 1
        ];
        
        if ($countryModel->insert($testCountry)) {
            echo "<p style='color: green;'>✓ Test country added successfully!</p>";
            $allCountries = $countryModel->findAll();
        } else {
            echo "<p style='color: red;'>✗ Failed to add test country</p>";
            echo "<pre>" . print_r($countryModel->errors(), true) . "</pre>";
        }
    }
    
    if (!empty($allCountries)) {
        echo "<p>Found " . count($allCountries) . " countries:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>ISO2</th><th>ISO3</th><th>Name</th><th>Deleted At</th></tr>";
        foreach ($allCountries as $country) {
            echo "<tr>";
            echo "<td>{$country['id']}</td>";
            echo "<td>{$country['iso2']}</td>";
            echo "<td>{$country['iso3']}</td>";
            echo "<td>{$country['name']}</td>";
            echo "<td>" . ($country['deleted_at'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>Active Countries (for dropdown)</h2>";
    $activeCountries = $countryModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
    
    if (empty($activeCountries)) {
        echo "<p style='color: orange;'>No active countries found!</p>";
    } else {
        echo "<p>Found " . count($activeCountries) . " active countries:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th></tr>";
        foreach ($activeCountries as $country) {
            echo "<tr>";
            echo "<td>{$country['id']}</td>";
            echo "<td>{$country['name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Dropdown HTML Preview</h3>";
        echo "<select style='padding: 8px; width: 300px;'>";
        echo "<option value=''>Select Country</option>";
        foreach ($activeCountries as $country) {
            echo "<option value='{$country['id']}'>" . htmlspecialchars($country['name']) . "</option>";
        }
        echo "</select>";
    }
    
    // Test the controller method
    echo "<h2>Controller Method Test</h2>";
    $controller = new \App\Controllers\DakoiiGovernmentController();
    
    // We can't easily test the view rendering, but we can test the data preparation
    echo "<p>Testing if controller can load countries...</p>";
    
    $countries = $countryModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
    if (!empty($countries)) {
        echo "<p style='color: green;'>✓ Controller can load countries data</p>";
        echo "<p>Countries that would be passed to view:</p>";
        echo "<ul>";
        foreach ($countries as $country) {
            echo "<li>{$country['name']} (ID: {$country['id']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Controller cannot load countries data</p>";
    }
    
} catch (\Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<p>1. <a href='http://localhost/promis_two/dakoii/government/countries'>View Countries List</a></p>";
echo "<p>2. <a href='http://localhost/promis_two/dakoii/government/countries/create'>Create New Country</a></p>";
echo "<p>3. <a href='http://localhost/promis_two/dakoii/government/provinces/create'>Test Province Create Form</a></p>";
?>
